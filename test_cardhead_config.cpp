/**
 * @file test_cardhead_config.cpp
 * @brief 卡头伸缩配置功能测试
 * <AUTHOR>
 * @date 2025-08-25
 */

#include <QCoreApplication>
#include <QSettings>
#include <QDebug>
#include <QDir>
#include <QTextStream>
#include <iostream>

/**
 * @brief 测试卡头伸缩配置读取功能
 */
void testCardHeadStretchConfig()
{
    qDebug() << "=== 卡头伸缩配置测试开始 ===";
    
    // 创建测试配置文件
    QString testConfigFile = "test_lane.ini";
    QSettings testConfig(testConfigFile, QSettings::IniFormat);
    
    // 测试用例1：默认值测试
    qDebug() << "\n测试用例1：默认值测试";
    testConfig.remove("laneinfo/CardHeadStretchConfig"); // 确保没有配置项
    int defaultValue = testConfig.value("laneinfo/CardHeadStretchConfig", 1).toInt();
    qDebug() << "默认值:" << defaultValue;
    Q_ASSERT(defaultValue == 1);
    qDebug() << "✓ 默认值测试通过";
    
    // 测试用例2：配置值为1
    qDebug() << "\n测试用例2：配置值为1";
    testConfig.setValue("laneinfo/CardHeadStretchConfig", 1);
    testConfig.sync();
    int value1 = testConfig.value("laneinfo/CardHeadStretchConfig", 1).toInt();
    qDebug() << "配置值1:" << value1;
    Q_ASSERT(value1 == 1);
    qDebug() << "✓ 配置值1测试通过";
    
    // 测试用例3：配置值为0
    qDebug() << "\n测试用例3：配置值为0";
    testConfig.setValue("laneinfo/CardHeadStretchConfig", 0);
    testConfig.sync();
    int value0 = testConfig.value("laneinfo/CardHeadStretchConfig", 1).toInt();
    qDebug() << "配置值0:" << value0;
    Q_ASSERT(value0 == 0);
    qDebug() << "✓ 配置值0测试通过";
    
    // 测试用例4：无效配置值处理
    qDebug() << "\n测试用例4：无效配置值处理";
    testConfig.setValue("laneinfo/CardHeadStretchConfig", 999);
    testConfig.sync();
    int invalidValue = testConfig.value("laneinfo/CardHeadStretchConfig", 1).toInt();
    qDebug() << "无效配置值:" << invalidValue;
    // 注意：这里读取到的是999，实际应用中需要在代码中进行范围检查
    
    // 清理测试文件
    QFile::remove(testConfigFile);
    
    qDebug() << "\n=== 卡头伸缩配置测试完成 ===";
}

/**
 * @brief 创建示例配置文件
 */
void createSampleConfig()
{
    qDebug() << "\n=== 创建示例配置文件 ===";
    
    QString sampleFile = "sample_lane.ini";
    QSettings sampleConfig(sampleFile, QSettings::IniFormat);
    
    // 添加laneinfo节的示例配置
    sampleConfig.setValue("laneinfo/lanetype", 1);
    sampleConfig.setValue("laneinfo/laneid", 1);
    sampleConfig.setValue("laneinfo/havecardmgr", true);
    sampleConfig.setValue("laneinfo/CardHeadStretchConfig", 1);
    
    // 添加CardMgr节的示例配置
    sampleConfig.setValue("CardMgr/DevType", 1);
    sampleConfig.setValue("CardMgr/Interval", 1800);
    
    sampleConfig.sync();
    
    qDebug() << "示例配置文件已创建:" << sampleFile;
    qDebug() << "配置内容:";
    qDebug() << "[laneinfo]";
    qDebug() << "lanetype=1";
    qDebug() << "laneid=1";
    qDebug() << "havecardmgr=true";
    qDebug() << "CardHeadStretchConfig=1  ; 1-依据车型判断，0-只要有线圈信号就伸出";
    qDebug() << "";
    qDebug() << "[CardMgr]";
    qDebug() << "DevType=1";
    qDebug() << "Interval=1800";
}

/**
 * @brief 模拟卡头伸缩逻辑测试
 */
void simulateCardHeadLogic()
{
    qDebug() << "\n=== 模拟卡头伸缩逻辑测试 ===";
    
    // 模拟不同配置下的逻辑
    struct TestCase {
        int config;
        bool hasLoopSignal;
        int vehClass;
        bool expectedResult;
        QString description;
    };
    
    QList<TestCase> testCases = {
        {1, true, 1, true, "配置1，有线圈信号，车型1（小车）"},
        {1, true, 11, true, "配置1，有线圈信号，车型11（货车1）"},
        {1, true, 3, false, "配置1，有线圈信号，车型3（大车）"},
        {0, true, 1, true, "配置0，有线圈信号，车型1"},
        {0, true, 3, true, "配置0，有线圈信号，车型3"},
        {0, false, 1, false, "配置0，无线圈信号，车型1"},
        {1, false, 1, false, "配置1，无线圈信号，车型1"}
    };
    
    for (const TestCase& testCase : testCases) {
        bool result = false;
        
        if (!testCase.hasLoopSignal) {
            result = false; // 无线圈信号，不伸出
        } else if (testCase.config == 0) {
            result = true; // 配置0，有线圈信号就伸出
        } else {
            // 配置1，根据车型判断
            // VC_Truck1 = 11, VC_Car3 = 3
            result = (testCase.vehClass == 11) || (testCase.vehClass < 3);
        }
        
        QString status = (result == testCase.expectedResult) ? "✓ 通过" : "✗ 失败";
        qDebug() << QString("%1 - 预期:%2, 实际:%3 %4")
                    .arg(testCase.description)
                    .arg(testCase.expectedResult ? "伸出" : "不伸出")
                    .arg(result ? "伸出" : "不伸出")
                    .arg(status);
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "卡头伸缩功能配置测试程序";
    qDebug() << "版本: 1.0";
    qDebug() << "日期: 2025-08-25";
    
    try {
        testCardHeadStretchConfig();
        createSampleConfig();
        simulateCardHeadLogic();
        
        qDebug() << "\n所有测试完成！";
        
    } catch (const std::exception& e) {
        qDebug() << "测试过程中发生错误:" << e.what();
        return 1;
    }
    
    return 0;
}
