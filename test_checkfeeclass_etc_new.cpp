/**
 * @file test_checkfeeclass_etc_new.cpp
 * @brief CheckFeeClass_Etc_New功能测试文件
 * <AUTHOR> Assistant
 * @date 2025-08-25
 */

#include <QCoreApplication>
#include <QDebug>
#include <QString>
#include <QDateTime>

// 包含必要的头文件
#include "AutoToll/transinfo.h"
#include "AutoToll/lanestate_vehinput.h"
#include "common/parafileold.h"

/**
 * @brief 测试CompareMinFee_New函数
 */
void testCompareMinFeeNew()
{
    qDebug() << "=== 测试CompareMinFee_New函数 ===";
    
    // 创建测试用的CTransInfo对象
    CTransInfo transInfo;
    
    // 设置测试数据
    transInfo.bIsJC = false;
    transInfo.MinFeeInfo.TotalFee = 1000;      // 最小费额基数
    transInfo.MinFeeInfo.TotalFee95 = 950;     // ETC 95折最小费额基数
    
    qint32 nPercent = 0;
    QString sError;
    
    // 测试用例1：正常情况，传入有效的倍率参数
    qDebug() << "测试用例1：正常情况，传入有效的倍率参数";
    int result1 = transInfo.CompareMinFee_New(1200, FeeClass_Card, nPercent, true, false, sError, 100, 200);
    qDebug() << "结果:" << result1 << "百分比:" << nPercent << "错误信息:" << sError;
    
    // 测试用例2：传入无效的倍率参数，应该使用默认值
    qDebug() << "测试用例2：传入无效的倍率参数，应该使用默认值";
    int result2 = transInfo.CompareMinFee_New(1200, FeeClass_Card, nPercent, true, false, sError, 0, 0);
    qDebug() << "结果:" << result2 << "百分比:" << nPercent << "错误信息:" << sError;
    
    // 测试用例3：费用低于最小费额
    qDebug() << "测试用例3：费用低于最小费额";
    int result3 = transInfo.CompareMinFee_New(800, FeeClass_Card, nPercent, true, false, sError, 100, 200);
    qDebug() << "结果:" << result3 << "百分比:" << nPercent << "错误信息:" << sError;
    
    // 测试用例4：费用超过最大费额
    qDebug() << "测试用例4：费用超过最大费额";
    int result4 = transInfo.CompareMinFee_New(2500, FeeClass_Card, nPercent, true, false, sError, 100, 200);
    qDebug() << "结果:" << result4 << "百分比:" << nPercent << "错误信息:" << sError;
    
    // 测试用例5：ETC计费方式
    qDebug() << "测试用例5：ETC计费方式";
    int result5 = transInfo.CompareMinFee_New(1100, FeeClass_OBU, nPercent, true, false, sError, 90, 180);
    qDebug() << "结果:" << result5 << "百分比:" << nPercent << "错误信息:" << sError;
    
    // 测试用例6：免费车辆
    qDebug() << "测试用例6：免费车辆";
    int result6 = transInfo.CompareMinFee_New(1200, FeeClass_Card, nPercent, true, true, sError, 100, 200);
    qDebug() << "结果:" << result6 << "百分比:" << nPercent << "错误信息:" << sError;
}

/**
 * @brief 测试参数倍率计算逻辑
 */
void testMinFeePercentCalculation()
{
    qDebug() << "=== 测试参数倍率计算逻辑 ===";
    
    // 模拟CMinFeePerCent结构
    CMinFeePerCent minFeePercent;
    minFeePercent.nVehClass = 1;
    minFeePercent.nType = 0;  // 本省
    minFeePercent.LowPercent_CPC = 90;
    minFeePercent.HightPercent_CPC = 150;
    minFeePercent.LowPercent_ETC = 85;
    minFeePercent.HightPercent_ETC = 180;
    
    // 测试ETC计费方式的倍率计算
    bool bETC = true;
    qint32 nLowPercent = 100, nHighPercent = 200;
    if (bETC) {
        nLowPercent = minFeePercent.LowPercent_ETC;
        nHighPercent = minFeePercent.HightPercent_ETC;
    } else {
        nLowPercent = minFeePercent.LowPercent_CPC;
        nHighPercent = minFeePercent.HightPercent_CPC;
    }
    
    qDebug() << "ETC计费方式 - 下限倍率:" << nLowPercent << "上限倍率:" << nHighPercent;
    
    // 测试CPC计费方式的倍率计算
    bETC = false;
    if (bETC) {
        nLowPercent = minFeePercent.LowPercent_ETC;
        nHighPercent = minFeePercent.HightPercent_ETC;
    } else {
        nLowPercent = minFeePercent.LowPercent_CPC;
        nHighPercent = minFeePercent.HightPercent_CPC;
    }
    
    qDebug() << "CPC计费方式 - 下限倍率:" << nLowPercent << "上限倍率:" << nHighPercent;
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "CheckFeeClass_Etc_New功能测试开始";
    qDebug() << "测试时间:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    
    // 执行测试
    testCompareMinFeeNew();
    testMinFeePercentCalculation();
    
    qDebug() << "测试完成";
    
    return 0;
}
