﻿#include "transinfo.h"

#include "cardfileconver_jiangxi.h"
#include "cardfileconverter.h"
#include "cfarecalcunit.h"
#include "cgreencarreservetabble.h"
#include "globalutils.h"
#include "laneinfo.h"
#include "messagebox.h"
#include "parafileold.h"
#include "paramfilemgr.h"
#include "speventmgr.h"
#include "tollgantrymgr.h"
#include "remotecontrolmgr.h"
#include "etclanectrl.h"
#include "mobilepay.h"
#include "vehplatefunc.h"

quint32 CTransInfo::GetConsumeMoney() { return ConsumeMoney; }

quint32 CTransInfo::GetLastMoney_Entry(qint32 &LastFee, qint32 &ConsumeFee)
{
    LastFee = gantryFeeInfo.realFee;  // gantryFeeInfo.payFee-gantryFeeInfo.discountFee;
    ConsumeFee = 0;                   //入口扣0元
    return LastFee;
}

/*
 * 返回 交易金额（实收）和实际扣费金额
 * 参数返回 实际扣费金额
 * 目前只用于最终显示
 */
quint32 CTransInfo::GetLastMoney_Exit(quint32 &ConsumeFee)
{
    ConsumeFee = this->ConsumeMoney;
    return m_nTransFee;
}

bool CTransInfo::CheckLastMoney_Exit(CFeeClass &feeClass, int nTransFee, qint32 &nPercent, bool isU,
                                     QString &sError, bool isLocal, bool bFree)
{
    const quint32 localFeeLimit_Min = 100;
    const quint32 otherFeeLimit_Min = 90;
    const quint32 FeeLimit_Max = 200;

    feeClass = FeeClass_OBU;
    nPercent = 0;
    if (!IccInfo.ef04Info.IsValid()) {
        return false;
    } else {
        if (this->bIsJC) {
            // DebugLog("手动计费不与最短费率比较");
            return true;
        }

        //实收金额为0，认为车辆免费，暂不认为是计算错误。
        if (0 == nTransFee && (bFree || isU)) return true;

        quint32 totalFee = nTransFee;
        quint32 totalFee_Min = MinFeeInfo.TotalFee95;

        quint32 FeeLow = totalFee;
        if (isLocal) {
            FeeLow = totalFee_Min * localFeeLimit_Min;
        } else
            FeeLow = totalFee_Min * otherFeeLimit_Min;

        quint32 FeeHigh = totalFee_Min * FeeLimit_Max;

        if (totalFee_Min > 0) {
            nPercent = totalFee * 100 / totalFee_Min;
            // U型车，超过最小，按最小，低于最小，按实收。
            quint32 totalFeePercent = totalFee * 100;  // Ptr_Info->IsMinFee(nPercent,sError);

            if (isU) {
                if (totalFeePercent > FeeHigh) {
                    sError = QString("U型通行费[%1]超出最小费额2倍,转人工").arg(totalFee);
                    return false;
                }
                if (totalFee > totalFee_Min) {
                    feeClass = FeeClass_Min;
                } else
                    feeClass = FeeClass_OBU;
                return true;
            }

            if (totalFeePercent > FeeHigh) {
                DebugLog(QString("超出最小金额2倍,totalFee:%1,totalFee_Min:%2,percent:%3")
                             .arg(totalFee)
                             .arg(totalFee_Min)
                             .arg(nPercent));
                sError = QString("通行费[%1]超出最小费额2倍,请转人工处理").arg(totalFee);
                return false;
            } else if (totalFeePercent < FeeLow) {
                sError = QString("通行费小于最小费额");
                feeClass = FeeClass_Min;
            } else
                feeClass = FeeClass_OBU;

            return true;
        } else {
            sError = QString("最小通行费为0,计费异常");
#ifdef QT_DEBUG
            return true;
#else
            return false;
#endif
        }
    }
    return false;
}

bool CTransInfo::CheckMinFee(qint32 totalFee, CFeeClass &feeClass, qint32 &nPercent, bool isU,
                             bool isLocal, bool bFree, QString &sError)
{
    const quint32 localFeeLimit_Min = 100;
    const quint32 otherFeeLimit_Min = 90;
    const quint32 FeeLimitETC_Max = 200;
    const quint32 FeeLimitCPC_MAX = 150;

    if (this->bIsJC) {
        return true;
    }
    if (bFree)  //免费车不比较最小费额
        return true;

    // U型0元不比较最小费率
    if ((0 == totalFee) && isU) {
        return true;
    }

    qint32 totalFee_Min = 0, totalFee_Max = 0;
    if (FeeClass_OBU == feeClass) {
        totalFee_Min = MinFeeInfo.TotalFee95;
        totalFee_Max = totalFee * FeeLimitETC_Max;
    } else {
        totalFee_Min = MinFeeInfo.TotalFee;
        totalFee_Max = totalFee * FeeLimitCPC_MAX;
    }

    if (isLocal) {
        totalFee_Min = totalFee_Min * localFeeLimit_Min;
    } else
        totalFee_Min = totalFee_Min * otherFeeLimit_Min;

    if (0 == totalFee_Min) {
        DebugLog(QString("最小通行费为0,计费异常"));
        sError = QString("最小通行费为0,计费异常");
        return false;
    }

    qint32 totalFeePercent = totalFee * 100;
    nPercent = totalFeePercent * 100 / totalFee_Min;
    if (isU) {
        //大于2或1.5倍 转在线
        if (totalFeePercent > totalFee_Max) {
            if (FeeClass_OBU == feeClass)
                sError = QString("U型车超出最小通行费2倍");

            else
                sError = QString("U型车超出最小通行费1.5倍");
            feeClass = isLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
            return true;
        } else {
            if (FeeClass_OBU == feeClass) {
                if (totalFeePercent > totalFee_Min) {
                    feeClass = FeeClass_Min;
                }
            } else if (FeeClass_Card == feeClass) {
            }
        }
        return true;
    }

    if (totalFeePercent > totalFee_Max) {
        feeClass = isLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
        if (FeeClass_OBU == feeClass)
            sError = QString("通行费超出最小费额2倍");
        else
            sError = QString("通行费超出最小费额1.5倍");
        return true;
    } else if (totalFeePercent < totalFee_Min) {
        if (MediaType_OBU == mediaType)
            feeClass = FeeClass_Min;
        else if (MediaType_CPC == mediaType) {
            feeClass = isLocal ? FeeClass_ProvCenter : FeeClass_MinistryCenter;
            return true;
        }
    } else {
        // feeClass 保持不变
        return true;
    }
    return false;
}

/**
 * @brief 比较最小通行费
 * @param
 * @return -1小于最小费额 0 计费正常 1大于最大费额对应倍数值
 */
int CTransInfo::CompareMinFee(qint32 totalFee, CFeeClass feeClass, qint32 &nPercent, bool isLocal,
                              bool bFree, QString &sError)
{
    //本省最小费率倍数
    const quint32 localFeeLimit_Min = 100;
    //外省最小费率倍数
    const quint32 otherFeeLimit_Min = 90;
    // ETC最高费额
    const quint32 FeeLimitETC_Max = 200;
    // CPC最高费额
    const quint32 FeeLimitCPC_MAX = 150;

    if (this->bIsJC) {
        return 0;
    }
    if (bFree)  //免费车不比较最小费额
        return 0;

    // U型0元不比较最小费率
    if (0 == totalFee) {
        // return 0;
    }

    int nVehAxisNum = GetVehAxisNum();
    quint32 totalFee_Min = 0, totalFee_Max = 0, MinFeeBase = 0;
    if (FeeClass_OBU == feeClass) {
        MinFeeBase = MinFeeInfo.TotalFee95;
        quint32 MinFeeHighBase = MinFeeInfo.TotalFee95;
        if (VehInfo.bNeedConfirmAxis() && nVehAxisNum > 6) {
            double fFee = MinFeeHighBase + MinFeeInfo1.TotalFee95 * (nVehAxisNum - 6) * 0.5;
            MinFeeHighBase = (quint32)fFee;
        }
        totalFee_Max = MinFeeHighBase * FeeLimitETC_Max;
    } else {
        MinFeeBase = MinFeeInfo.TotalFee;
        quint32 MinFeeHighBase = MinFeeInfo.TotalFee;
        if (VehInfo.bNeedConfirmAxis() && nVehAxisNum > 6) {
            double fFee = MinFeeHighBase + MinFeeInfo1.TotalFee * (nVehAxisNum - 6) * 0.5;
            MinFeeHighBase = (quint32)fFee;
        }
        totalFee_Max = MinFeeHighBase * FeeLimitCPC_MAX;
    }

    if (isLocal) {
        totalFee_Min = MinFeeBase * localFeeLimit_Min;
    } else
        totalFee_Min = MinFeeBase * otherFeeLimit_Min;

    if (0 == totalFee_Min) {
        DebugLog(QString("最小通行费为0,计费异常"));
        sError = QString("最小通行费为0,计费异常");
        return 0;
    }

    DebugLog(
        QString("最小费额比较,计费方式:%1,计费金额:%2,最小通行费:%3,最终最小费额:%4,最大费额:%5")
            .arg(feeClass)
            .arg(totalFee)
            .arg(MinFeeBase)
            .arg(totalFee_Min)
            .arg(totalFee_Max));

    quint32 totalFeePercent = totalFee * 100;
    if (MinFeeBase > 0) nPercent = totalFeePercent * 100 / MinFeeBase;

    if (totalFeePercent > totalFee_Max) {
        if (FeeClass_OBU == feeClass)
            sError = QString("超出最小费额2倍");
        else
            sError = QString("超出最小费额1.5倍");
        return 1;
    }
    if (totalFeePercent < totalFee_Min) {
        return -1;
    }
    //等于或大于1倍小于2倍按实际
    return 0;
}

/**
 * @brief 比较最小通行费_新版本
 * @param totalFee 总费用
 * @param feeClass 计费方式
 * @param nPercent 百分比
 * @param isLocal 是否本地
 * @param bFree 是否免费
 * @param sError 错误信息
 * @param nLowPercent 下限倍率，如果为0则使用默认值
 * @param nHighPercent 上限倍率，如果为0则使用默认值
 * @return -1小于最小费额 0 计费正常 1大于最大费额对应倍数值
 */
int CTransInfo::CompareMinFee_New(qint32 totalFee, CFeeClass feeClass, qint32 &nPercent, bool isLocal,
                                  bool bFree, QString &sError, qint32 nLowPercent, qint32 nHighPercent)
{
    //本省最小费率倍数
    const quint32 localFeeLimit_Min = 100;
    //外省最小费率倍数
    const quint32 otherFeeLimit_Min = 90;
    // ETC最高费额
    const quint32 FeeLimitETC_Max = 200;
    // CPC最高费额
    const quint32 FeeLimitCPC_MAX = 150;

    if (this->bIsJC) {
        return 0;
    }
    if (bFree)  //免费车不比较最小费额
        return 0;

    // U型0元不比较最小费率
    if (0 == totalFee) {
        // return 0;
    }

    qint32 totalFee_Min = 0, totalFee_Max = 0;
    qint32 MinFeeBase = 0;
    if (FeeClass_OBU == feeClass) {
        MinFeeBase = MinFeeInfo.TotalFee95;
    } else {
        MinFeeBase = MinFeeInfo.TotalFee;
    }

    // 使用传入的倍率参数，如果传入的倍率有误则使用原来的默认倍率
    qint32 useLowPercent, useHighPercent;
    if (nLowPercent > 0 && nHighPercent > 0 && nLowPercent <= nHighPercent) {
        useLowPercent = nLowPercent;
        useHighPercent = nHighPercent;
    } else {
        // 使用默认倍率
        if (isLocal) {
            useLowPercent = localFeeLimit_Min;
        } else {
            useLowPercent = otherFeeLimit_Min;
        }

        if (FeeClass_OBU == feeClass) {
            useHighPercent = FeeLimitETC_Max;
        } else {
            useHighPercent = FeeLimitCPC_MAX;
        }
    }

    totalFee_Min = MinFeeBase * useLowPercent;
    totalFee_Max = MinFeeBase * useHighPercent;

    if (0 == totalFee_Min) {
        DebugLog(QString("最小通行费为0,计费异常"));
        sError = QString("最小通行费为0,计费异常");
        return 0;
    }

    DebugLog(
        QString("最小费额比较_新版本,计费方式:%1,计费金额:%2,最小通行费:%3,最终最小费额:%4,最大费额:%5,使用倍率:%6-%7")
            .arg(feeClass)
            .arg(totalFee)
            .arg(MinFeeBase)
            .arg(totalFee_Min)
            .arg(totalFee_Max)
            .arg(useLowPercent/100.0)
            .arg(useHighPercent/100.0));

    quint32 totalFeePercent = totalFee * 100;
    if (MinFeeBase > 0) nPercent = totalFeePercent * 100 / MinFeeBase;

    if (totalFeePercent > totalFee_Max) {
        //if (FeeClass_OBU == feeClass)
            sError = QString("超出最小费额上限倍率 %1").arg(useHighPercent/100.0);
        //else
        //    sError = QString("超出最小费额上限倍率");
        return 1;
    }
    if (totalFeePercent < totalFee_Min) {
        sError = QString("小于最小费额下限倍率 %1").arg(useLowPercent/100.0);
        return -1;
    }
    //等于或大于下限小于上限按实际
    return 0;
}

int CTransInfo::CompareMinFeeByParam(qint32 totalFee, bool bETC, int LowPercent, int HighPercent,
                                     qint32 &nPercent, bool bFree, QString &sError)
{
    if (bFree) return 0;

    if (bIsJC) {
        return 0;
    }

    if (0 == totalFee) {
        //有可能是U型车计费为0，此处也进行比对
        DebugLog(QString("非免费车通行费为0"));
    }
    quint32 totalFee_Min = 0, totalFee_Max = 0, MinFeeBase = 0;
    bool bFeeByAxles = VehInfo.bNeedConfirmAxis();
    int nAxisNum = GetVehAxisNum();
    // ETC
    if (bETC) {
        MinFeeBase = MinFeeInfo.TotalFee95;
        quint32 MinFeeHighBase = MinFeeInfo.TotalFee95;
        if (bFeeByAxles && nAxisNum > 6) {
            double fFee = MinFeeHighBase + MinFeeInfo1.TotalFee95 * (nAxisNum - 6) * 0.5;
            MinFeeHighBase = (quint32)fFee;
        }
        totalFee_Max = MinFeeHighBase * HighPercent;
    } else {  // cpc
        MinFeeBase = MinFeeInfo.TotalFee;
        quint32 MinFeeHighBase = MinFeeInfo.TotalFee;
        if (bFeeByAxles && nAxisNum > 6) {
            double fFee = MinFeeHighBase + MinFeeInfo1.TotalFee * (nAxisNum - 6) * 0.5;
            MinFeeHighBase = (quint32)fFee;
        }
        totalFee_Max = MinFeeHighBase * HighPercent;
    }

    totalFee_Min = MinFeeBase * LowPercent;

    DebugLog(QString("最小费额系数比较,计费金额:%1,最小通行费:%2,最终最小费额:%3,最大费额:%4,"
                     "最高比例:%5,最低比例:%6")
                 .arg(totalFee)
                 .arg(MinFeeBase)
                 .arg(totalFee_Min)
                 .arg(totalFee_Max)
                 .arg(HighPercent)
                 .arg(LowPercent));

    quint32 totalFeePercent = totalFee * 100;
    if (MinFeeBase > 0) nPercent = totalFeePercent / MinFeeBase;

    if (totalFeePercent > totalFee_Max) {
        sError = QString("超出最小费额上限");
        DebugLog(QString("通行费%1超出最消费额上限%2,百分比:%3")
                     .arg(totalFeePercent)
                     .arg(totalFee_Max)
                     .arg(HighPercent));
        return 1;
    } else if (totalFeePercent < totalFee_Min) {
        DebugLog(QString("通行费%1小于收费下限%2,百分比:%3")
                     .arg(totalFeePercent)
                     .arg(totalFee_Min)
                     .arg(LowPercent));
        return -1;
    }
    //等于或大于1倍小于2倍按实际
    return 0;
}

bool CTransInfo::GetTollMoney_Exit(qint32 &totalPayFee, qint32 &totalRealFee, qint32 &nCardCost)
{
    totalPayFee = m_nTotalFee;
    totalRealFee = m_nTransFee;
    nCardCost = m_nCardCost;
    return true;
    /*
    if(FeeClass_OBU==feeClass){
        totalPayFee = IccInfo.ef04Info.totalFee_After;
        totalRealFee = IccInfo.ef04Info.totalLastFee_After;
    }else if(FeeClass_Card==feeClass){
        quint32 nTotalMiles=0,nTotalFee=0;
        bool bRlt=cpcIccInfo.cpcTollCellInfo.GetTotalFee(nTotalMiles,nTotalFee);
        if(!bRlt)
            return false;
        totalRealFee = (nTotalFee+50)/100*100;
        totalPayFee = totalRealFee;
    }else if(FeeClass_Min==feeClass){
        return false;
    }else if(FeeClass_ProvCenter==feeClass || FeeClass_MinistryCenter==feeClass){
        return false;
    }
    return true;
    */
}

void CTransInfo::SetCalcMoneyToEF04_Exit(bool bFree, qint32 nDiscountType)
{
    actualFeeClass = FeeClass_OBU;
    bIsFree = bFree;
    m_nDiscountType = nDiscountType;

    qint32 nFeeProvSumLocal = gantryFeeInfo.feeProvSumLocal;
    qint32 nPayFeeSumLocal = gantryFeeInfo.payFeeProvSumLocal;
    qint32 nMiles = gantryFeeInfo.feeMileage;

    IccInfo.ef04Info.AddCurTransInfo(nFeeProvSumLocal, nPayFeeSumLocal, nMiles);

    m_nTotalFee = IccInfo.ef04Info.totalFee_After;
    m_nTransFee = IccInfo.ef04Info.totalLastFee_After;
    m_nDiscountFee = m_nTotalFee - m_nTransFee;
    ConsumeMoney = m_nTransFee;
    m_nOriginFee = 0;
    m_nProvinceDiscountFee = 0;

    if (bFree) {
        //节假日免费
        DebugLog("免除车辆通行费");
        IccInfo.ef04Info.totalLastFee_After = 0;  //总实收为0
        IccInfo.ef04Info.localLastFee_After = 0;  //本省实收也应该为0

        //如果是中心免费，填中心免费字段
        if (nDiscountType > 0) {
            if (6 == nDiscountType || 7 == nDiscountType) {
                m_nOriginFee = m_nTransFee;
                m_nProvinceDiscountFee = m_nOriginFee;
            }
        }
        //更新优惠金额
        m_nTransFee = 0;
        m_nDiscountFee = m_nTotalFee - m_nTransFee - m_nProvinceDiscountFee;
        ConsumeMoney = m_nTransFee;
    }
    return;
}

void CTransInfo::SumCalcMoney_Exit(CFeeClass nFeeClass, bool bFree, bool bETCTrans,
                                   qint32 nDiscountType)
{
    actualFeeClass = nFeeClass;

    if (FeeClass_OBU == nFeeClass) {
        qint32 nRealFee = gantryFeeInfo.realFee;
        qint32 nFeeProvSumLocal = gantryFeeInfo.feeProvSumLocal;
        qint32 nPayFeeSumLocal =
            gantryFeeInfo.payFeeProvSumLocal;  // cpc卡为四舍五入后的金额，etc为折扣前金额
        qint32 nMiles = gantryFeeInfo.feeMileage;
        quint32 nProvMiles = gantryFeeInfo.provSumFeeMileage;

        IccInfo.ef04Info.AddCurTransInfo(nFeeProvSumLocal, nPayFeeSumLocal, nMiles);
        /*
        if (bFree) {
            IccInfo.ef04Info.totalLastFee_After = 0;
            IccInfo.ef04Info.localLastFee_After = 0;
        }*/
    } else if (FeeClass_Card == nFeeClass) {  // cpc 目前记录的都是应收
        qint32 nRealFee = gantryFeeInfo.realFee;
        qint32 nFeeProvSumLocal = gantryFeeInfo.feeProvSumLocal;
        qint32 nPayFeeSumLocal =
            gantryFeeInfo.payFeeProvSumLocal;  // cpc卡为四舍五入后的金额，etc为折扣前金额
        qint32 nMiles = gantryFeeInfo.feeMileage;
        quint32 nProvMiles = gantryFeeInfo.provSumFeeMileage;
        cpcIccInfo.cpcRoadInfo.AddCurTransInfo(nRealFee, nPayFeeSumLocal, nProvMiles, nMiles);
        cpcIccInfo.cpcRoadInfo.nProvFlagCnt_After = cpcIccInfo.cpcRoadInfo.nProvFlagCnt + 1;
        cpcIccInfo.cpcRoadInfo.sNewFlag_After = m_curGantryInfo.sGantryHex;
        cpcIccInfo.cpcRoadInfo.nNewFlagPassTime_After = TransTime.toTime_t();

        if (0 == gantryFeeInfo.fitProvFlag) {
            cpcIccInfo.cpcRoadInfo.bPassFlagCnt_After = cpcIccInfo.cpcRoadInfo.bPassFlagCnt + 1;
            cpcIccInfo.cpcRoadInfo.FlagInfoList_After =
                cpcIccInfo.cpcRoadInfo.FlagInfoList + cpcIccInfo.cpcRoadInfo.sNewFlag_After;
        } else {
            cpcIccInfo.cpcRoadInfo.bPassFlagCnt_After = gantryFeeInfo.gantryPassNumFit;
            cpcIccInfo.cpcRoadInfo.FlagInfoList_After = gantryFeeInfo.gantryPassHexFit;
        }
        CProvCellInfo prvCellInfo;
        prvCellInfo.bProv = 36;
        prvCellInfo.bProvFlagCnt = cpcIccInfo.cpcRoadInfo.nProvFlagCnt_After;
        prvCellInfo.bSign = 0;
        prvCellInfo.nProvTollMoney = nPayFeeSumLocal;
        prvCellInfo.nPayProvTollMoney = prvCellInfo.nProvTollMoney;
        prvCellInfo.nProvTradeMeter = nProvMiles;
        prvCellInfo.nPassTime = cpcIccInfo.cpcRoadInfo.nProvEnFlagPassTime;
        prvCellInfo.sEnFlag = cpcIccInfo.cpcRoadInfo.sProvEnFlag;
        prvCellInfo.sNewFlag = cpcIccInfo.cpcRoadInfo.sNewFlag_After;
        prvCellInfo.nNewPassTime = cpcIccInfo.cpcRoadInfo.nNewFlagPassTime_After;
        cpcIccInfo.cpcTollCellInfo_After.clear();
        cpcIccInfo.cpcTollCellInfo_After =
            cpcIccInfo.cpcTollCellInfo;  //.AddProvCellInfo(prvCellInfo);
        cpcIccInfo.cpcTollCellInfo_After.AddProvCellInfo(prvCellInfo);
        QString sLog = cpcIccInfo.cpcTollCellInfo_After.Log();
        DebugLog(sLog);
    } else if (FeeClass_Min == nFeeClass) {
    } else if (FeeClass_ProvCenter == nFeeClass || FeeClass_MinistryCenter == nFeeClass) {
    }
    RefreshMoney(bFree, nDiscountType);
    return;
}

void CTransInfo::FreeCalcMoney_Exit(int nDiscountType)
{
    DebugLog(QString("免除车辆通行费,中心免费类型%1").arg(nDiscountType));
    //如果是中心免费，填中心免费字段
    // bIsFree = true;
    m_nOriginFee = 0;
    m_nProvinceDiscountFee = 0;
    // m_nDiscountType = nDiscountType;
    if (nDiscountType > 0) {
        // if (6 == nDiscountType || 7 == nDiscountType) {
        m_nOriginFee = m_nTransFee;
        m_nProvinceDiscountFee = m_nOriginFee;
        //}
    }
    //更新优惠金额
    m_nProvFee = 0;
    m_nTransFee = 0;
    m_nDiscountFee = m_nTotalFee - m_nTransFee - m_nProvinceDiscountFee;
    return;
}

//根据最新计费信息刷新总的计费金额
void CTransInfo::RefreshMoney(bool bFree, qint32 nDiscountType)
{
    switch (actualFeeClass) {
        case FeeClass_OBU: {
            m_nTotalFee = IccInfo.ef04Info.totalFee_After;
            m_nTransFee = IccInfo.ef04Info.totalLastFee_After;
            m_nTotalMiles = IccInfo.ef04Info.totalTollMiles_After;
            m_nProvFee = IccInfo.ef04Info.localLastFee_After;
            m_nProvinceCount = IccInfo.ef04Info.bProvinceCount_After;
            break;
        }
        case FeeClass_Card: {
            qint32 nTotalMiles = 0, nTotalFee = 0, nPayFee = 0;
            if (!cpcIccInfo.cpcTollCellInfo_After.GetTotalFee(nTotalMiles, nTotalFee, nPayFee)) {
                ErrorLog("refresh money cpc GetToalFee return false");
            }
            m_nTotalFee = (nPayFee + 50) / 100 * 100;
            m_nTransFee = (nTotalFee + 50) / 100 * 100;
            m_nTotalMiles = nTotalMiles;
            m_nProvFee = cpcIccInfo.cpcRoadInfo.nProvTollMoney_After;
            m_nProvinceCount = cpcIccInfo.cpcRoadInfo.nProvCnt_After;
            DebugLog(QString("卡内金额计费"));
            break;
        }
        case FeeClass_Min: {
            m_nTotalFee = MinFeeInfo.PayTotalFee;
            m_nTransFee = MinFeeInfo.GetLastFee(mediaType);
            m_nProvFee = MinFeeInfo.GetLocalFee(mediaType);
            m_nTotalMiles = MinFeeInfo.totalMiles;
            m_nProvinceCount = MinFeeInfo.provMinFeeGroup.size();
            break;
        }
        case FeeClass_ProvCenter:
        case FeeClass_MinistryCenter: {
            m_nTotalFee = m_fareQryResult.GetPayFee(mediaType);
            m_nTransFee = m_fareQryResult.GetFee(mediaType);
            m_nProvFee = m_fareQryResult.GetProvFee(mediaType);
            m_nTotalMiles = m_fareQryResult.feeMileage;
            m_nProvinceCount = m_fareQryResult.provinceFees.size();
            break;
        }
        default:
            return;
            break;
    }

    if (m_nTotalFee > m_nTransFee)
        m_nDiscountFee = m_nTotalFee - m_nTransFee;
    else
        m_nDiscountFee = 0;

    m_nOriginFee = 0;
    m_nProvinceDiscountFee = 0;
    bIsFree = bFree;
    m_nDiscountType = nDiscountType;
    if (bFree) FreeCalcMoney_Exit(nDiscountType);
    return;
}

void CTransInfo::SetLastMoney(qint32 nPayFee, qint32 nRealMoney, quint32 dwCardMoney,
                              CFeeClass bFeeClass, quint32 minPercent)
{
    //扣费信息
    m_nTotalFee = nPayFee;
    m_nTransFee = nRealMoney;
    ConsumeMoney = dwCardMoney;
    actualFeeClass = bFeeClass;
    nMinPerCent = minPercent;
    m_nDiscountFee = m_nTotalFee - m_nTransFee - m_nProvinceDiscountFee;

    DebugLog(QString("最终收费，应收:%1,实收:%2,扣费:%3,计费方式:%4,优惠金额:%5,省中心优惠类型:%6,"
                     "省中心优惠前金额:%7省中心优惠金额:%8")
                 .arg(m_nTotalFee)
                 .arg(m_nTransFee)
                 .arg(ConsumeMoney)
                 .arg(actualFeeClass)
                 .arg(m_nDiscountFee)
                 .arg(m_nDiscountType)
                 .arg(m_nOriginFee)
                 .arg(m_nProvinceDiscountFee));

    //保存分省计费信息
    if (MediaType_OBU == mediaType) {
        CProvinceFeeInfo provinceFeeInfo;
        provinceFeeInfo.proviceId = 0x36;
        /*
        if (FeeClass_Min == bFeeClass) {
            provinceFeeInfo.provinceFee = MinFeeInfo.localTotalFee95;
        } else
            provinceFeeInfo.provinceFee = IccInfo.ef04Info.localLastFee_After;  //分省计费
            */
        provinceFeeInfo.provinceFee = m_nProvFee;
        IccInfo.ef04Info.provinceFeeGroup.push_back(provinceFeeInfo);
    }
}

/*
 * 参数： dwTotalFee -应收金额 dwDiscountFee -折扣优惠金额  dwFee
 * -交易金额（实收）dwLocalFee本省累计实收 dwConsumeFee 最终扣费金额 collectFee 代收外省金额
 */
void CTransInfo::GetAllMoneyInfo(qint32 &dwTotalFee, qint32 &dwDiscountFee, qint32 &dwFee,
                                 qint32 &dwLocalFee, qint32 &dwConsumeFee, qint32 &collectFee)
{
    collectFee = 0;
    dwTotalFee = m_nTotalFee;
    dwFee = m_nTransFee;
    dwDiscountFee = m_nDiscountFee;  //  dwTotalFee - dwFee;
    dwLocalFee = m_nProvFee;
    dwConsumeFee = ConsumeMoney;
    if (dwFee >= dwLocalFee) collectFee = dwFee - dwLocalFee;
    return;
    /*
        if (actualFeeClass == FeeClass_OBU) {
            if (bIsFree)
                dwLocalFee = 0;
            else
                dwLocalFee = IccInfo.ef04Info.localLastFee_After;
            dwConsumeFee = ConsumeMoney;
            if (IccInfo.ef04Info.bProvinceCount_After == 1) {
                collectFee = 0;
            } else {
                if (dwFee > dwLocalFee) collectFee = dwFee - dwLocalFee;
            }

        } else if (actualFeeClass == FeeClass_Min) {
            dwConsumeFee = ConsumeMoney;
            if (bIsFree)
                dwLocalFee = 0;
            else
                dwLocalFee = MinFeeInfo.GetLocalFee(mediaType);

        } else if (FeeClass_Card == actualFeeClass) {
            if (bIsFree)
                dwLocalFee = 0;
            else
                dwLocalFee = cpcIccInfo.cpcRoadInfo.nProvTollMoney_After;
            if (bIsFree)
                collectFee = 0;
            else
                collectFee = cpcIccInfo.cpcTollCellInfo_After.GetCollectFee();
        } else if (FeeClass_MinistryCenter == actualFeeClass || FeeClass_ProvCenter ==
       actualFeeClass) { if (bIsFree) dwLocalFee = 0; else dwLocalFee =
       m_fareQryResult.GetProvFee(this->mediaType);

        } else {
            dwTotalFee = 0;     // gantryFeeInfo.payFee;
            dwDiscountFee = 0;  // gantryFeeInfo.discountFee;
            dwFee = 0;          // dwTotalFee -dwDiscountFee;
            dwConsumeFee = 0;   // ConsumeMoney;
            dwLocalFee = 0;     // dwFee;
        }

        if (dwFee >= dwLocalFee) collectFee = dwFee - dwLocalFee;
        return;
        */
}

void CTransInfo::FillEF04Info_Entry(CPro0019Raw_NewGB *pRaw0019)
{
    IccInfo.ef04Info.clear();
    memcpy(&IccInfo.ef04Info.raw0015, &IccInfo.Raw15, sizeof &IccInfo.ef04Info.raw0015);
    memcpy(&IccInfo.ef04Info.raw0019, pRaw0019, sizeof IccInfo.ef04Info.raw0019);
    IccInfo.ef04Info.bProvinceCount =
        1;  //该字段用于判断ef04是否有效，赋值之后即为有效信息。相当于之前有过入口写卡，因此省份数是1
            // todo考虑
    IccInfo.ef04Info.bProvinceCount_After = 1;
    IccInfo.ef04Info.totalFee_After = gantryFeeInfo.payFee;
    IccInfo.ef04Info.totalLastFee_After = gantryFeeInfo.payFee - gantryFeeInfo.discountFee;
    IccInfo.ef04Info.totalTransOkTimes = 0;
    IccInfo.ef04Info.totalTransOkTimes_After = 1;
    IccInfo.ef04Info.totalTollMiles = 0;  // feeInfo.feeMileage;// 新版计费模块
    IccInfo.ef04Info.totalTollMiles_After = gantryFeeInfo.feeMileage;
    IccInfo.ef04Info.bTotalNoCardTimes = 0;
    IccInfo.ef04Info.bTotalNoCardTimes_After = 0;
    IccInfo.ef04Info.sLocalEntryIdHex = IccInfo.CardTollInfo.sEnStationHex;
    IccInfo.ef04Info.localTotalFee_After = gantryFeeInfo.payFee;
    IccInfo.ef04Info.bLocalTransOkTimes = 0;
    IccInfo.ef04Info.bLocalTransOkTimes_After = 1;
    IccInfo.ef04Info.localLastFee_After = gantryFeeInfo.payFee - gantryFeeInfo.discountFee;
    // IccInfo.ef04Info.feeProvSumLocal = IccInfo.ef04Info.localLastFee_After;

    CProvinceFeeInfo ProvincefeeInfo;
    ProvincefeeInfo.proviceId = 0x36;
    ProvincefeeInfo.provinceFee = IccInfo.ef04Info.localLastFee_After;
    IccInfo.ef04Info.provinceFeeGroup.push_back(ProvincefeeInfo);
}

void CTransInfo::SetAllRoadMinFeeInfo(const QList<CMinFeeInfo_Province> &minFeeGroup)
{
    MinFeeInfo.SetProvMinFeeGroup(minFeeGroup);
}

void CTransInfo::SetAllRoadMinFeeInfo(const CAllRoadMinFeeInfo &minFeeInfo,
                                      const CAllRoadMinFeeInfo *pMinFeeInfo1)
{
    MinFeeInfo = minFeeInfo;
    if (pMinFeeInfo1) {
        MinFeeInfo1 = *pMinFeeInfo1;
    }
}

void CTransInfo::GetMinFeeInfo(qint32 &minFee, quint32 &Miles)
{
    if (MediaType_OBU == mediaType) {
        minFee = MinFeeInfo.TotalFee95;
        Miles = MinFeeInfo.totalMiles;
    } else {
        minFee = MinFeeInfo.TotalFee;
        Miles = MinFeeInfo.totalMiles;
    }
}

QString CTransInfo::GetPassId() const
{
    if (MediaType_OBU == mediaType) {
        QString sCardNetId = QString::fromAscii(IccInfo.ProCardBasicInfo.szNetworkId);
        QString sCardId = QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        QString sOBUId = QString::fromAscii(this->OBUBaseInfo.szContractSerialNumber);
        if (sOBUId.length() > 0) {
            return QString("01%1%2%3")
                .arg(sCardNetId)
                .arg(sCardId)
                .arg(this->vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
        } else if (sCardNetId.length() > 0) {
            return QString("01%1%2%3")
                .arg(sCardNetId)
                .arg(sCardId)
                .arg(this->vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
        } else {
            return QString("");
        }
        /*
        if (m_transPayType == TransPT_ETCCard) {  // etc 刷卡支付
            return QString("01%1%2%3")
                .arg(sCardNetId)
                .arg(sCardId)
                .arg(this->vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
        }
        //以下为etc交易
        switch (etcTransType) {
            case TransType_EnNormal:
            case TransType_EnOnlyAppTrans: {
                return QString("01%1%2%3")
                    .arg(sCardNetId)
                    .arg(sCardId)
                    .arg(TransTime.toString("yyyyMMddhhmmss"));
                break;
            }
            case TransType_EnNoCard:
            case TransType_EnStoreNoBlance: {
                return QString("000000%1%2").arg(sOBUId).arg(TransTime.toString("yyyyMMddhhmmss"));
                break;
            }
            case TransType_ExNormal:
            case TransType_ExOnlyAppTrans:
            case TransType_ExStoreCardNoBalance:
            case TransType_ExBlack: {
                return QString("01%1%2%3")
                    .arg(sCardNetId)
                    .arg(sCardId)
                    .arg(this->vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
                break;
            }
            case TransType_None: {
                return QString("%1%2").arg(sOBUId).arg(
                    vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
            }
            default:
                break;
        }
        return QString("030%1%2")
            .arg(Ptr_Info->GetGBLaneId())
            .arg(TransTime.toString("yyyyMMddhhmmss"));
            */
    } else if (MediaType_CPC == mediaType) {
        return QString("020000%1%2")
            .arg(QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID))
            .arg(vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));

    } else if (MediaType_Paper == mediaType) {
        return QString("040000%1%2").arg(m_sPaperId).arg(TransTime.toString("yyyyMMddhhmmss"));
    } else if (MediaType_None == mediaType) {
        return QString("030%1%2")
            .arg(Ptr_Info->GetGBLaneId())
            .arg(TransTime.toString("yyyyMMddhhmmss"));
    } else {
        return QString("");
    }
}

QString CTransInfo::GetPassId_New() const
{
    if (MediaType_OBU == mediaType) {
        QString sCardNetId = QString::fromAscii(IccInfo.ProCardBasicInfo.szNetworkId);
        QString sCardId = QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        QString sOBUId = QString::fromAscii(this->OBUBaseInfo.szContractSerialNumber);
        if (m_transPayType == TransPT_ETCCard) {  // etc 刷卡支付
            return QString("01%1%2%3")
                .arg(sCardNetId)
                .arg(sCardId)
                .arg(this->vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
        }
        //以下为etc交易
        switch (etcTransType) {
            case TransType_EnNormal:
            case TransType_EnOnlyAppTrans: {
                return QString("01%1%2%3")
                    .arg(sCardNetId)
                    .arg(sCardId)
                    .arg(TransTime.toString("yyyyMMddhhmmss"));
                break;
            }
            case TransType_EnNoCard:
            case TransType_EnStoreNoBlance: {
                return QString("000000%1%2").arg(sOBUId).arg(TransTime.toString("yyyyMMddhhmmss"));
                break;
            }
            case TransType_ExNormal:
            case TransType_ExOnlyAppTrans:
            case TransType_ExStoreCardNoBalance:
            case TransType_ExBlack: {
                return QString("01%1%2%3")
                    .arg(sCardNetId)
                    .arg(sCardId)
                    .arg(this->vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
                break;
            }
            case TransType_None: {
                return QString("%1%2").arg(sOBUId).arg(
                    vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
            }
            default:
                break;
        }
        return QString("030%1%2")
            .arg(Ptr_Info->GetGBLaneId())
            .arg(TransTime.toString("yyyyMMddhhmmss"));
    } else if (MediaType_CPC == mediaType) {
        return QString("020000%1%2")
            .arg(QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID))
            .arg(vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
    } else if (MediaType_Paper == mediaType) {
        return QString("040000%1%2").arg(m_sPaperId).arg(TransTime.toString("yyyyMMddhhmmss"));
    } else
        return QString("030%1%2")
            .arg(Ptr_Info->GetGBLaneId())
            .arg(TransTime.toString("yyyyMMddhhmmss"));
}

quint8 CTransInfo::GetFeeBoard(QString &sMsg)
{
    if (transResult < Tr_Successed) {
        return 2;  //警告
    }

    if (Ptr_Info->IsEntryLane()) {
        return 1;
    }

    if (IccInfo.ef04Info.IsValid()) {
        if (IccInfo.ef04Info.bTotalNoCardTimes > 0) {
            sMsg = QString("请规范使用ETC");
            return 2;
        }
        if (nMinPerCent < 80 && nMinPerCent > 0) {
            sMsg = QString("请规范使用ETC");
            return 2;
        }
        return 1;
    } else {
        return 1;
    }
}

void CTransInfo::SetTradeFeeInfo(const TradingInfo &tradingInfo, const FeeInfo &fee, bool isJC)
{
    memcpy(&tradInfo, &tradingInfo, sizeof tradingInfo);
    memcpy(&feeInfo, &fee, sizeof feeInfo);
    CalcFee::FeeInfoToGantryFeeInfo(fee, gantryFeeInfo);
    this->bIsJC = isJC;

    QString sLog = QString(
                       "计费成功,payFee:%1,"
                       "discountFee:%2,"
                       "cardFee:%3,realFee:%4,"
                       "feeProvSumLocal:%5,"
                       "payFeeProvSumLocal:%6,"
                       "feaMileage:%7,provSumFeeMileage:%8")
                       .arg(gantryFeeInfo.payFee)
                       .arg(gantryFeeInfo.discountFee)
                       .arg(gantryFeeInfo.cardFee)
                       .arg(gantryFeeInfo.realFee)
                       .arg(gantryFeeInfo.feeProvSumLocal)
                       .arg(gantryFeeInfo.payFeeProvSumLocal)  // feeSpare1
                       .arg(gantryFeeInfo.feeMileage)
                       .arg(gantryFeeInfo.provSumFeeMileage);  // feeSpare2
    DebugLog(sLog);

    sLog = QString("sFeeSpecial:%1,fitResult:%2,tollIntervalIDs:%3,tollIntervalSign:%4")
               .arg(gantryFeeInfo.sFeeSpecial)
               .arg(gantryFeeInfo.fitResult)
               .arg(gantryFeeInfo.tollIntervalIDs)
               .arg(gantryFeeInfo.tollIntervalSign);
    DebugLog(sLog);
    sLog = QString(
               "payFeeGroup:%1,discountFeeGroup:%2,feeGroup:%3,"
               "rateFitCount:%4,feeVehicleType:%5,"
               "feeProvBeginHexFit:%6,feeProvBeginTimeFit:%7,provMinFee:%8")
               .arg(gantryFeeInfo.payFeeGroup)
               .arg(gantryFeeInfo.discountFeeGroup)
               .arg(gantryFeeInfo.feeGroup)
               .arg(gantryFeeInfo.rateFitCount)
               .arg(gantryFeeInfo.feeVehicleType)
               .arg(gantryFeeInfo.feeProvBeginHexFit)
               .arg(gantryFeeInfo.feeProvBeginTimeFit)
               .arg(gantryFeeInfo.provMinFee);
    DebugLog(sLog);

    DebugLog(QString("fitProvFlag:%1,gantryPassNumFit:%2,gantryPassHexFit:%3")
                 .arg(gantryFeeInfo.fitProvFlag)
                 .arg(gantryFeeInfo.gantryPassNumFit)
                 .arg(gantryFeeInfo.gantryPassHexFit));

    DebugLog(QString("feeInfo1:%1,feeInfo2:%2,feeInfo3:%3,feeLogMsg:%4")
                 .arg(gantryFeeInfo.feeInfo1)
                 .arg(gantryFeeInfo.feeInfo2)
                 .arg(gantryFeeInfo.feeInfo3)
                 .arg(gantryFeeInfo.feeLogMsg));
    return;
}

QString CTransInfo::GetTransTypeDesc()
{
    switch (etcTransType) {
        case TransType_None: {
            return QString("");
            break;
        }
        case TransType_EnNormal: {  //正常发卡扣费,ef01+19
            return QString("");
            break;
        }
        case TransType_ExNormal: {
            return QString("");
            break;
        }
        case TransType_abNormal: {  //异常扣费（需要拦截）
            return QString("异常");
            break;
        }
        case TransType_EnNoCard:  // ef04(BB+入口信息)
        {
            return QString("未插卡");
            break;
        }
        case TransType_EnStoreNoBlance:  // ef04 (aa+入口)
        {
            return QString("余额为0");
            break;
        }
        case TransType_ExStoreCardNoBalance:  // 0-消费 -清空EF04
        {
            return QString("余额为0");
            break;
        }
        case TransType_ExBlack: {  //
            return QString("出口黑名单车辆");
        }
        default:
            break;
    }
    return QString("");
}

void CTransInfo::SetGBSpEvent(GBSPEVENTID spEventId, bool bStatus)
{
    GBSpEvent[spEventId] = bStatus ? '1' : '0';
    return;
}

QString CTransInfo::GetOncePaySpTypes()
{
    QString sResult;
    quint8 tmpSpEvent[OncePay_Sp_End];
    memset(tmpSpEvent, 0, sizeof tmpSpEvent);
    memcpy(tmpSpEvent, OncePaySpEvent, sizeof OncePaySpEvent);

    if (MediaType_OBU == mediaType && dwOBUID > 0) {
        if (OBUBaseInfo.OBUState.bLowPower) tmpSpEvent[OncePay_Sp_OBULow] = 1;
    }
    if (MediaType_CPC == mediaType && m_bBadCard) {
        tmpSpEvent[OncePay_Sp_CPCBad] = 1;
    }
    /*
    if (MediaType_None == mediaType) {
        tmpSpEvent[OncePay_Sp_NoCPC] = 1;
    }*/

    if (Ptr_Info->IsExitLane()) {
        if (VehInfo.VehClass != vehEntryInfo.bEnVC) tmpSpEvent[OncePay_Sp_EnVCDiff] = 1;
        QString svlp = GB2312toUnicode(VehInfo.szVehPlate);
        QString svlpEn = GB2312toUnicode(vehEntryInfo.szEnVLP);
        if ((VehInfo.nVehPlateColor != vehEntryInfo.bEnVLPC) || (svlp != svlpEn)) {
            tmpSpEvent[OncePay_Sp_EnVLPDiff] = 1;
        }
        if (vehEntryInfo.bVehState != 0xff) {
            if (m_bVehState != vehEntryInfo.bVehState) {
                tmpSpEvent[OncePay_Sp_EnVehStateDiff] = 1;
            }
        }

        if (!isCar(VehInfo.VehClass)) {  //客车就不比较轴数了
            int nAxisNum = GetVehAxisNum();
            if (vehEntryInfo.VehicalAxles > 0 && vehEntryInfo.VehicalAxles < 0xff) {
                if (nAxisNum > 0 && nAxisNum != vehEntryInfo.VehicalAxles) {
                    tmpSpEvent[OncePay_Sp_EnAxlesDiff] = 1;
                }
            }
        }

        if (bHolidayFree) tmpSpEvent[OncePay_Sp_HolidayFree] = 1;

        if (m_nOutTimes > 0) tmpSpEvent[OncePay_Sp_PassTimeHigh] = 1;
        if (m_bGreenCheckFailed) tmpSpEvent[OncePay_Sp_GreenError] = 1;

        if (VehInfo.GBVehType == UVT_FarmProduct) tmpSpEvent[OncePay_Sp_GreenFree] = 1;
        if (VehInfo.GBVehType == UVT_CombinHarvester) tmpSpEvent[OncePay_Sp_Harvister] = 1;
    }
    if (bInvoice) tmpSpEvent[OncePay_Sp_ViolateVeh] = 1;

    for (int i = 0; i < OncePay_Sp_End; ++i) {
        if (1 == tmpSpEvent[i]) {
            sResult += QString("|%1").arg(i);
        }
    }
    if (sResult.length() > 0) {
        sResult.remove(0, 1);
    }
    return sResult;
}

void CTransInfo::SetEmVehInfo(bool bEmVeh, const CEmVehInfo &emVehInfo)
{
    m_bEmVeh = bEmVeh;
    if (bEmVeh) {
        m_emVehInfo = emVehInfo;
        if (m_emVehInfo.vehicleSign == QString("0x05")) {
            m_bVehState = VehState_Vaccin;

        } else if (m_emVehInfo.vehicleSign == QString("0x06")) {
            m_bVehState = VehState_EmergencyResue;
        }
    } else {
        m_emVehInfo.ClearEmVehInfo();
        if (m_bVehState == VehState_Vaccin || m_bVehState == VehState_EmergencyResue)
            m_bVehState = VehState_Def;
    }
    return;
}

bool CTransInfo::QryFare_OnLine(CFareQryCondition &qryCondition, CFareQryResult &fareResult,
                                QString &sError, bool bDiscount)
{
    qryCondition.ClearCondition();
    qryCondition.mediaType = mediaType;
    qryCondition.isDiscount = bDiscount ? 1 : 2;
    qryCondition.enStationId = vehEntryInfo.sEnGBStationId;
    qryCondition.enTollLaneId = vehEntryInfo.sEnGBLaneId;
    qryCondition.enTime = QDateTime2GBTimeStr(vehEntryInfo.EnTime);
    qryCondition.exStationId = Ptr_Info->GetGBStationId();
    qryCondition.exTollLaneId = Ptr_Info->GetGBLaneId();
    qryCondition.exTime = QDateTime2GBTimeStr(QDateTime::currentDateTime());
    QString sPlate = GB2312toUnicode(VehInfo.szVehPlate);
    qryCondition.vehicleId = QString("%1_%2").arg(sPlate).arg(VehInfo.nVehPlateColor);
    qryCondition.axleCount = GetVehAxisNum();
    qryCondition.vehicleType = VehInfo.VehClass;

    if (MediaType_OBU == mediaType) {
        qryCondition.mediaNo = QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        // qryCondition.ETCCARD0015 = Raw2HexStr(IccInfo.);

    } else if (MediaType_CPC == mediaType) {
        qryCondition.mediaNo = QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID);
        qryCondition.CPCMFEF01 = cpcIccInfo.MFef01.toHex().toUpper();
        qryCondition.CPCMFEF02 = cpcIccInfo.MFef02.toHex().toUpper();
        qryCondition.CPCEF01 = cpcIccInfo.cpcTollnfoRaw.toHex().toUpper();
        qryCondition.CPCEF02 = cpcIccInfo.cpcpRoadInfoRaw.toHex().toUpper();
        qryCondition.CPCEF04 = cpcIccInfo.cpcTollCellInfoRaw.toHex().toUpper();
    } else if (MediaType_Paper == mediaType) {
        qryCondition.mediaNo = m_sPaperId;
    }

    bool bRlt = CFareCalcUnit::QryFare_OnLine(qryCondition, fareResult);
    if (!bRlt) {
        sError = QString("在线计费失败");
        return false;
    }

    return true;
}

bool CTransInfo::QryFare_OnLine_Union(CFareQryCondition &qryCondition, CFareQryResult &fareResult,
                                      QString &sError, bool bDiscount, const QDateTime &TransTime)
{
    qryCondition.ClearCondition();
    qryCondition.mediaType = mediaType;
    qryCondition.isDiscount = bDiscount ? 1 : 2;
    qryCondition.enStationId = vehEntryInfo.sEnGBStationId;
    qryCondition.enTollLaneId = vehEntryInfo.sEnGBLaneId;
    qryCondition.enTime = QDateTime2GBTimeStr(vehEntryInfo.EnTime);
    /*
    QDateTime curTime = QDateTime::currentDateTime();
    if (vehEntryInfo.nEntryType == Entry_ByManual) {
        QDateTime enTime =
        qryCondition.enTime = QString("");
    } else
        qryCondition.enTime = QDateTime2GBTimeStr(vehEntryInfo.EnTime);
        */
    qryCondition.exStationId = Ptr_Info->GetGBStationId();
    qryCondition.exTollLaneId = Ptr_Info->GetGBLaneId();
    qryCondition.exTime = QDateTime2GBTimeStr(TransTime);
    QString sPlate = GB2312toUnicode(VehInfo.szVehPlate);
    qryCondition.vehicleId = QString("%1_%2").arg(sPlate).arg(VehInfo.nVehPlateColor);
    qryCondition.axleCount = GetVehAxisNum();
    //
    if (isTruck(VehInfo.VehClass)) {
        //出口不处理称重，或者没称台，取入口车辆轴数
        if ((!Ptr_Info->bCheckWeight()) || m_vehAxisInfo.IsNullVeh() ||
            0x00ffffff == m_dwToTalWeight || 0 == m_dwToTalWeight) {
            qryCondition.axleCount = vehEntryInfo.VehicalAxles;
            DebugLog(
                QString("货车在线计费,出口无称重信息,取入口轴数:%1").arg(qryCondition.axleCount));
        }
    }
    if (0 == qryCondition.axleCount || qryCondition.axleCount > VehWeight_MaxAlxeNum) {
        int nAxleCnt = GetVehAxisNumByVC(VehInfo.VehClass);
        qryCondition.axleCount = nAxleCnt;
        DebugLog(
            QString("在线计费车辆轴数%1,按车型取轴数%2").arg(qryCondition.axleCount).arg(nAxleCnt));
    }
    qryCondition.vehicleType = VehInfo.VehClass;

    qryCondition.issuerId.clear();
    qryCondition.validShortFee = 1;
    qryCondition.vehicleClass = VehInfo.GBVehType;
    qryCondition.vehicleSign = QString("0x%1").arg(m_bVehState, 2, 16, QLatin1Char('0')).toLower();

    if (MediaType_OBU == mediaType) {
        QString sCardNetId = QString::fromAscii(IccInfo.ProCardBasicInfo.szNetworkId);
        QString sCardId = QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        //        QString sOBUId = QString::fromAscii(this->OBUBaseInfo.szContractSerialNumber);
        qryCondition.passId = QString("01%1%2%3")
                                  .arg(sCardNetId)
                                  .arg(sCardId)
                                  .arg(vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
        // mediaNo 获取不到可以不填，
        // qryCondition.mediaNo = QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        qryCondition.vehicleUserType = IccInfo.ProCardBasicInfo.bUserType;
        qryCondition.ETCCARD0002.clear();
        qryCondition.ETCCARD0015 = Raw2HexStr((quint8 *)&IccInfo.Raw15, sizeof IccInfo.Raw15);
        qryCondition.ETCCARD0019 = Raw2HexStr((quint8 *)&IccInfo.Raw19, sizeof IccInfo.Raw19);
        qryCondition.cardId = QString("%1%2")
                                  .arg(QString::fromAscii(IccInfo.ProCardBasicInfo.szNetworkId))
                                  .arg(QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo));

    } else if (MediaType_CPC == mediaType) {
        qryCondition.mediaNo = QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID);
        qryCondition.passId = QString("020000%1%2")
                                  .arg(QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID))
                                  .arg(vehEntryInfo.EnTime.toString("yyyyMMddhhmmss"));
        qryCondition.CPCMFEF01 = cpcIccInfo.MFef01.toHex().toUpper();
        qryCondition.CPCMFEF02 = cpcIccInfo.MFef02.toHex().toUpper();
        qryCondition.CPCEF01 = cpcIccInfo.cpcTollnfoRaw.toHex().toUpper();
        qryCondition.CPCEF02 = cpcIccInfo.cpcpRoadInfoRaw.toHex().toUpper();
        qryCondition.CPCEF04 = cpcIccInfo.cpcTollCellInfoRaw.toHex().toUpper();
        qint32 nTotalMiles = 0;
        qint32 nTotalFee = 0;
        qint32 nPayFee = 0;
        if (cpcIccInfo.cpcTollCellInfo_After.GetTotalFee(nTotalMiles, nTotalFee, nPayFee)) {
            qryCondition.payFee = QString::number((nPayFee + 50) / 100 * 100);
            qryCondition.fee = QString::number((nTotalFee + 50) / 100 * 100);
            qryCondition.discountFee = QString::number(nPayFee - nTotalFee);
        }

    } else if (MediaType_Paper == mediaType) {
        qryCondition.mediaNo = QString("01") + m_sPaperId;
    } else {
        qryCondition.mediaNo = QString("030");
    }

    bool bLoadFromFile = false;
#ifdef QT_DEBUG
    bLoadFromFile = true;
#endif
    bool bRlt = CFareCalcUnit::QryFare_OnLine_Union(qryCondition, fareResult, bLoadFromFile);
    if (!bRlt) {
        sError = QString("跨省在线计费失败");
        return false;
    }

    return true;
}

bool CTransInfo::QryFare_OnLine_Prov(CFareQryCondition &qryCondition, CFareQryResult &fareResult,
                                     QString &sError, bool bDiscount, const QDateTime &TransTime)
{
    qryCondition.ClearCondition();
    qryCondition.mediaType = mediaType;
    qryCondition.isDiscount = bDiscount ? 1 : 2;
    qryCondition.enTime = QDateTime2GBTimeStr(vehEntryInfo.EnTime);
    qryCondition.enStationId = vehEntryInfo.sEnGBStationId;
    qryCondition.exStationId = Ptr_Info->GetGBStationId();
    qryCondition.exTime = QDateTime2GBTimeStr(TransTime);
    qryCondition.vlp = GB2312toUnicode(VehInfo.szVehPlate);
    qryCondition.vlpc = VehInfo.nVehPlateColor;
    qryCondition.vehicleType = VehInfo.VehClass;
    qryCondition.vehicleClass = VehInfo.GBVehType;
    if (mediaType == MediaType_OBU) {
        qryCondition.mediaNo = QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        qryCondition.payType = 4;
        qryCondition.sPayCardId = QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        if (IccInfo.ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD)
            qryCondition.feeSumLocal = IccInfo.dwBalance;

        qryCondition.vehicleUserType = IccInfo.ProCardBasicInfo.bUserType;
        qryCondition.cardNetWork = IccInfo.ProCardBasicInfo.szNetworkId;
        qryCondition.tagType = 1;
        qryCondition.cardType = IccInfo.ProCardBasicInfo.bType;
        qryCondition.cardVer = IccInfo.ProCardBasicInfo.bVersion;
        qryCondition.cpuCardId = QString::fromAscii(IccInfo.ProCardBasicInfo.szNetworkId) +
                                 QString::fromAscii(IccInfo.ProCardBasicInfo.szCardNo);
        qryCondition.vehicleWeightLimits = 0;
        qryCondition.obuSn.clear();
        qryCondition.feeProvBeginHex.clear();

    } else if (MediaType_CPC == mediaType) {
        qryCondition.mediaNo = QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID);
        qryCondition.payType = 1;
        QString sGantryId = cpcIccInfo.cpcRoadInfo.GetFlagStr();
        int nGantryCnt = sGantryId.length() / 3;
        for (int i = 0; i < nGantryCnt; ++i) {
            qryCondition.gantryIdGroup = qryCondition.gantryIdGroup + "|" + sGantryId.mid(i, 3);
        }
        if (qryCondition.gantryIdGroup.length() > 0) qryCondition.gantryIdGroup.remove(0, 1);
        qryCondition.cardNetWork = QString("0");

        qryCondition.tagType = 2;
        qryCondition.vehicleWeightLimits = 0;
        qryCondition.obuSn = QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID);
    } else if (MediaType_Paper == mediaType) {
        qryCondition.mediaNo = m_sPaperId;
        qryCondition.payType = 1;
    }

    qryCondition.laneStatus = vehEntryInfo.bEnLaneType;
    qryCondition.lastGantryId = m_curGantryInfo.sGantryId;
    qryCondition.lastGantryTime = QDateTime2GBTimeStr(TransTime);
    qryCondition.issuerId.clear();
    qryCondition.enAxleCount = vehEntryInfo.VehicalAxles;
    if (qryCondition.enAxleCount == 0 || qryCondition.enAxleCount > 12) {
        qryCondition.enAxleCount = GetVehAxisNumByVC(VehInfo.VehClass);
    }
    qryCondition.enVehicleType = vehEntryInfo.bEnVC;
    qryCondition.vehicleStatusFlag = VehInfo.GBVehType;
    int nAxisNum = GetVehAxisNum();
    qryCondition.axleCount = nAxisNum;
    if (0 == nAxisNum || nAxisNum > VehWeight_MaxAlxeNum) {
        qryCondition.axleCount = GetVehAxisNumByVC(VehInfo.VehClass);
    }
    if (isTruck(VehInfo.VehClass)) {
        qryCondition.totalWeight = m_dwToTalWeight;
    }

    bool bRlt = CFareCalcUnit::QryFare_OnLine_Prov(qryCondition, fareResult);
    if (!bRlt) {
        sError = QString("省内在线计费失败");
        return false;
    }
    return true;
}

void CTransInfo::SetFareQryResult(const CFareQryCondition &qryCondition,
                                  const CFareQryResult &fareResult)
{
    m_fareQryCondition = qryCondition;
    m_fareQryResult = fareResult;
}

void CTransInfo::SetGBVehType(CUnionVehType gbVehType)
{
    VehInfo.GBVehType = gbVehType;
    m_bVehState = GetVehStateByVehType(gbVehType);
    return;
}

bool CTransInfo::VerifyAreaCode_ProCard(const CProCardBasicInfo &proCardBasicInfo,
                                        qint32 &nErrorCode, QString &sError)
{
    if (proCardBasicInfo.bType != CARD_TYPE_STORE_CARD &&
        CARD_TYPE_TALLY_CARD != proCardBasicInfo.bType) {
        nErrorCode = CSpEventMgr::SpEvent_AbnormalCard;
        sError = QString("卡类型[%1]错误").arg(proCardBasicInfo.bType);
        return false;
    }
    CAreaCodeTable *pAreaCodeTable = (CAreaCodeTable *)CParamFileMgr::GetParamFile(cfAreaCode);
    if (!pAreaCodeTable) {
        sError = QString("区域参数表不存在");
        return false;
    }
    QString sCardIssuer = GB2312toUnicode(proCardBasicInfo.IssueOrgId, 4);
    DebugLog(QString("检验卡片区域信息:%1,%2").arg(proCardBasicInfo.wNetWorkId).arg(sCardIssuer));
    CAreaCode AreaCode;
    bool bFinded = pAreaCodeTable->QryAreaCode(proCardBasicInfo.wNetWorkId, sCardIssuer, &AreaCode);

    if (!bFinded) {
        nErrorCode = CSpEventMgr::SpEvent_NotLocalCard;
        sError = QString("非联网ETC卡[%1]").arg(proCardBasicInfo.wNetWorkId);
        return false;
    }
    return true;
}

bool CTransInfo::VerifyProCardBasicInfo(const CVehInfo &VehInfo,
                                        const CProCardBasicInfo &proCardBasicInfo,
                                        qint32 &nErrorCode, QString &sError)
{
    bool bYJVeh = CCardFileConverter::IsYJCard(proCardBasicInfo);

    //判断卡片过期
    QDate sDate = QDate::currentDate();
    QDateTime useTime, expiredTime;

    QString sVehPlateInCard = GB2312toUnicode(proCardBasicInfo.szVehPlate).trimmed();
    QString sVehPlate = GB2312toUnicode(VehInfo.szVehPlate).trimmed();

    if (sVehPlateInCard != sVehPlate) {
        //车卡不符
        nErrorCode = CSpEventMgr::SpEvent_NotSelfCard;
        sError = QString("非本车卡,卡片[%1]实际车牌[%2]").arg(sVehPlateInCard).arg(sVehPlate);
        return false;
    }

    //    if (proCardBasicInfo.bVehPlateColor != VehInfo.nVehPlateColor) {
    //        ErrorLog(QString("车卡绑定车牌颜色不符,卡内车牌颜色:%1,OBU内车牌颜色:%2")
    //                     .arg(proCardBasicInfo.bVehPlateColor)
    //                     .arg(VehInfo.nVehPlateColor));
    //        nErrorCode = CSpEventMgr::SpEvent_NotSelfCard;
    //        sError = QString("非本车卡,车牌颜色不符[%1][%2]")
    //                     .arg(proCardBasicInfo.bVehPlateColor)
    //                     .arg(VehInfo.nVehPlateColor);
    //        return false;
    //    }

    if (bYJVeh) return true;

    if (ConvertChar14ToDateTime(useTime, proCardBasicInfo.szStartTime)) {
        if (sDate < useTime.date()) {
            DebugLog(QString("卡片启用时间:%1").arg(useTime.toString("yyyy-MM-dd")));
            nErrorCode = CSpEventMgr::SpEvent_UnUseCard;
            sError = QString("尚未启用卡片[%1]").arg(useTime.toString("yyyy-MM-dd"));
            return false;
        }
    }

    if (ConvertChar14ToDateTime(expiredTime, proCardBasicInfo.szExpireTime)) {
        DebugLog(QString("卡片过期时间:%1").arg(expiredTime.toString("yyyy-MM-dd")));
        if (sDate > expiredTime.date()) {
            nErrorCode = CSpEventMgr::SpEvent_OutTimeCard;
            sError = QString("过期卡[%1]").arg(expiredTime.toString("yyyy-MM-dd"));
            return false;
        }
    }
    return true;
}

void CTransInfo::GetPro0019RawForExit(CPro0019Raw_NewGB &Raw0019)
{
    memset(&Raw0019, 0, sizeof Raw0019);
    CCardTollInfo cardTollInfo = IccInfo.CardTollInfo;
    cardTollInfo.sEnStationHex = Ptr_Info->GetHexStationID().right(4);
    cardTollInfo.sNetworkHex = ORG_NETWORKID_HEX;
    cardTollInfo.sEnLaneHex = Ptr_Info->GetHexLaneID().right(2);
    cardTollInfo.bLaneId = Ptr_Info->GetLaneId();
    cardTollInfo.szPassTime = TransTime;
    cardTollInfo.sLastGantryHex = CTollGantryMgr::GetTollGantryMgr()->GetGantryHex();
    cardTollInfo.bPassStatus = Ptr_Info->GetLaneType();
    if (Ptr_Info->CheckReplaceWriteCard()) {
        cardTollInfo.sLastGantryHex = Ptr_Info->GetReplaceGantryHex();
        cardTollInfo.bPassStatus = 0x0F;
        if (cardTollInfo.sLastGantryHex.isEmpty()) {
            cardTollInfo.sLastGantryHex = CTollGantryMgr::GetTollGantryMgr()->GetGantryHex();
            // Raw0019.File0012Raw_GB.bPassStatus = Ptr_Info->GetLaneType();
        }
    } else {
        // Raw0019.File0012Raw_GB.bPassStatus = Ptr_Info->GetLaneType();
    }
    cardTollInfo.dwDoorFramePassTime = QDateTime2UnixTime_GB(TransTime);
    cardTollInfo.dwTotalFee = 0;
    CCardFileConverter::CardTollInfo2Pro0019Raw_NewGB(&Raw0019, &cardTollInfo);
    return;
}

void CTransInfo::SetVirGantryInfo(const CVirGantryInfo &gantryInfo, bool bOpen,
                                  const QString &sVLPId)
{
    m_curGantryInfo = gantryInfo;
    m_bOpenGantry = bOpen;
    m_sVLPId = sVLPId;

    if (bOpen) {
        if (m_sVLPId.isEmpty()) {
            QString sGantryId = m_curGantryInfo.sGantryId;
            QString sGantryHex = m_curGantryInfo.sGantryHex;
            QString sBatch, sNo;
            m_sVehicleSignId =
                sGantryId + CBatchMgr::GetBatchMgr()->GetBatchInfo(sBatch, sNo, sGantryHex,
                                                                   CBatchMgr::SnType_VLP, false);
            DebugLog(QString("生成开放式门架图像id:%1").arg(m_sVehicleSignId));
        }
    }
}

bool CTransInfo::CheckOutTime(QString &sError)
{
    QDateTime curTime = QDateTime::currentDateTime();
    if (vehEntryInfo.nEntryType != Entry_ByCard && vehEntryInfo.nEntryType != Entry_ByQry) {
        return false;
    } else {
        if (vehEntryInfo.nEntryType == Entry_ByCard || vehEntryInfo.nEntryType == Entry_ByQry) {
            if (vehEntryInfo.nPassTime > 60 * 60 * 24 * 7) {
                sError = QString("行驶超过7天,入口时间:%1")
                             .arg(vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss"));
                return true;
            }
        }
    }

    if (m_bUReturn) {  // U型不计算超时
        return false;
    }

    //必须算完钱才可以
    if (actualFeeClass == FeeClass_None) return false;

    int nMiles = m_nTotalMiles;
    DebugLog(
        QString("超时判断,车辆行驶里程:%1米,入口类型:%2").arg(nMiles).arg(vehEntryInfo.nEntryType));

    if (0 == nMiles) {
        return false;
    }

    int nSpeed = 60000 / 60;                   //速度单位米/分钟
    quint32 nCalcTime = nMiles / nSpeed * 60;  //行驶时间单位秒

    QDateTime enTime = vehEntryInfo.EnTime;
    DebugLog(QString("检查车辆超时,计费里程:%1,允许行驶时间%2,入口时间:%3")
                 .arg(nMiles)
                 .arg(nCalcTime)
                 .arg(enTime.toString("yyyy-MM-dd hh:mm:ss")));

    if (enTime < curTime) {
        qint32 nFactTime = enTime.secsTo(curTime);
        if (nFactTime > nCalcTime) {
            m_nOutTimes = nFactTime - nCalcTime;
            DebugLog(QString("车辆行驶时间%1,允许时间:%2,超时%1秒")
                         .arg(nFactTime)
                         .arg(nCalcTime)
                         .arg(m_nOutTimes));

            if (m_nOutTimes < 60) {
                DebugLog(QString("超时时间%1,少于1分钟,忽略不计").arg(m_nOutTimes));
                m_nOutTimes = 0;
                return false;
            }

            if (m_nOutTimes < Ptr_Info->GetMaxOutTime() * 60) {
                return false;
            }

            int nHours = m_nOutTimes / 3600;
            int nMinutes = (m_nOutTimes % 3600) / 60;

            QString sEnTime = vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss");
            if (nHours > 0) {
                sError = QString("车辆超时%1小时%2分钟 入口站:%3 入口时间:%4")
                             .arg(nHours)
                             .arg(nMinutes)
                             .arg(vehEntryInfo.sEnStaionName)
                             .arg(sEnTime);
            } else {
                sError = QString("车辆超时%1分钟").arg(nMinutes);
            }
            return true;
        }
    }
    return false;
}

void CTransInfo::SetVcrResult(const VcrResult *pVcrResult)
{
    if (pVcrResult)
        m_vcrResult = *pVcrResult;
    else
        m_vcrResult.ClearResult();
}

bool CTransInfo::SetFeeInfoL(const FeeInfoL &feeInfoL)
{
    if (actualFeeClass == FeeClass_OBU) {
        if (!this->IccInfo.ef04Info.UpdateFee(feeInfoL.payFee, feeInfoL.realFee)) {
            return false;
        }
    } else if (FeeClass_Card == actualFeeClass) {
        if (!cpcIccInfo.UpdateFeeInfo(feeInfoL.payFee, feeInfoL.realFee)) return false;
    } else if (FeeClass_Min == actualFeeClass) {
        if (!MinFeeInfo.UpdateFee(feeInfoL.payFee, feeInfoL.realFee)) return false;
    } else if (FeeClass_MinistryCenter == actualFeeClass || FeeClass_ProvCenter == actualFeeClass) {
        int nPayFee = m_fareQryResult.payFee;
        int nFee = m_fareQryResult.fee;
        int nSplitFee = m_fareQryResult.splitAmount;
        int nDiscountFee = m_fareQryResult.discountFee;
        if (m_fareQryResult.provinceFees.size() > 1) {
            DebugLog(QString("当前在线计费结果中含有多省计费信息%1,不进行差异化计费")
                         .arg(m_fareQryResult.GetProvinceFeeGroup(0, false)));
            return false;
        }

        if (!m_fareQryResult.UpdateProveFee(feeInfoL.payFee, feeInfoL.realFee, feeInfoL.disFee)) {
            DebugLog(QString("在线计费差异化计费,更新分省计费失败"));
            return false;
        }

        m_fareQryResult.payFee = feeInfoL.payFee;
        m_fareQryResult.fee = feeInfoL.realFee;
        m_fareQryResult.splitAmount = feeInfoL.realFee;
        m_fareQryResult.discountFee = feeInfoL.disFee;
        DebugLog(QString("差异化计费,更新在线计费结果,原,PayFee:%1,fee:%2,splitAmount:%3,"
                         "discountFee:%4,差异化后,%5,%6,%7,%8")
                     .arg(nPayFee)
                     .arg(nFee)
                     .arg(nSplitFee)
                     .arg(nDiscountFee)
                     .arg(m_fareQryResult.payFee)
                     .arg(m_fareQryResult.fee)
                     .arg(m_fareQryResult.splitAmount)
                     .arg(m_fareQryResult.discountFee));
    }

    RefreshMoney(false, m_nDiscountType);
    m_feeInfoL = feeInfoL;
    m_bDifferentFee = true;
    return true;
}

void CTransInfo::ClearFeeInfoL()
{
    m_bDifferentFee = false;
    memset(&m_feeInfoL, 0, sizeof m_feeInfoL);
}

bool CTransInfo::ProcessDiffFeeInfo(bool bLocal)
{
    ClearFeeInfoL();
    if (!bLocal) return false;
    SpParaTable *pTable = (SpParaTable *)CParamFileMgr::GetParamFile(cfSpPara);
    if (!pTable) return false;
    bool bDiffFee = pTable->IsDifferentFeeStation(Ptr_Info->GetStationID());
    if (!bDiffFee) return false;
    FeeInfoL feeInfoL;
    bool bRlt = CFareCalcUnit::CalcFeeL(this, bLocal, feeInfoL);
    if (!bRlt) return false;
    return this->SetFeeInfoL(feeInfoL);
}

void CTransInfo::GetFeeInfoL(QString &disCode, QString &feeInfo1)
{
    if (!m_bDifferentFee) return;
    disCode = QString::fromLocal8Bit(m_feeInfoL.disCode);
    feeInfo1 = QString::fromLocal8Bit(m_feeInfoL.feeInfo1);
    return;
}

/**
 * @brief
 * @param bForEntry 出口纸卡、坏卡 无卡填入口信息时用，如果有称重，以称重为准
 * @return
 */
int CTransInfo::GetVehAxisNum(bool bForEntry)
{
    //只有校验轴数时才认为当前轴数正确，否则取入口轴数
    if (m_nAxisNum > 0) {
        if (bForEntry || Ptr_Info->bCheckWeight()) return m_nAxisNum;
    }

    if (Ptr_Info->IsExitLane()) {
        //出口如果轴数为0，表示没有称重信息,以入口轴数为准。如果入口轴数异常，按车型取
        int nAxleNum = 0;
        nAxleNum = vehEntryInfo.VehicalAxles;
        if (0 == nAxleNum || nAxleNum > VehWeight_MaxAlxeNum) {
            nAxleNum = GetVehAxisNumByVC(VehInfo.VehClass);
        }
        return nAxleNum;
    } else {
        //入口
        int nAxleNum = GetVehAxisNumByVC(VehInfo.VehClass);
        return nAxleNum;
    }
}

bool CTransInfo::CheckVehEntryAxisNum(QString &sError)
{
    if (!Ptr_Info->bCheckWeight()) {
        DebugLog(QString("当前车道不处理称重"));
        return true;
    }

    if (!VehInfo.bNeedConfirmAxis()) return true;

    if (m_nAxisNum > 0) {
        if (m_nAxisNum != vehEntryInfo.VehicalAxles) {
            sError = QString("入口%1轴,出口%2轴").arg(vehEntryInfo.VehicalAxles).arg(m_nAxisNum);
            return false;
        }
    }
    return true;
}

bool CTransInfo::bSpecialVeh(QString &sVehPlate)
{
    sVehPlate = GB2312toUnicode(this->VehInfo.szVehPlate);
    int nIndex1 = sVehPlate.indexOf(QString("赣O"));
    int nIndex2 = sVehPlate.indexOf(QString("警"));
    if (nIndex1 >= 0 && nIndex2 > 0) {
        return true;
    }
    return false;
}

//bool CTransInfo::IsLocalETCPoliceVeh(QString &sVehPlate)
//{
//    // 检查OBU车牌是否包含"赣O"
//    QString sOBUVehPlate = GB2312toUnicode(this->VehInfo.szVehPlate);
//    if (::IsLocalETCPoliceVeh(sOBUVehPlate)) {
//        sVehPlate = sOBUVehPlate;
//        return true;
//    }

//    // 检查ETC卡内车牌（B3帧信息）是否包含"赣O"
//    QString sETCVehPlate = GB2312toUnicode(this->OBUVehInfo.szVehPlate);
//    if (::IsLocalETCPoliceVeh(sETCVehPlate)) {
//        sVehPlate = sETCVehPlate;
//        return true;
//    }

//    // 如果都不包含"赣O"，返回false
//    sVehPlate = sOBUVehPlate.isEmpty() ? sETCVehPlate : sOBUVehPlate;
//    return false;
//}

qint64 CTransInfo::GetVehSpeed()
{
    time_t curTime = QDateTime::currentDateTime().toTime_t();

    qint64 defSpeed = 25 + curTime % 10;
    //时间异常
    if ((0 == lSpeedStartTime) || (0 == lSpeedStopTime) || (lSpeedStartTime >= lSpeedStopTime) ||
        (lSpeedStopTime - lSpeedStartTime > 20000)) {
        return defSpeed;
    }

    qint64 vspeed = Ptr_Info->GetLaneLen() * 3600 / (lSpeedStopTime - lSpeedStartTime);
    if (vspeed > 100 || vspeed < 5) {
        vspeed = defSpeed;
    }
    return vspeed;
}

void CTransInfo::SetTransState(CTransInfo::CTransState tsState)
{
    transState = tsState;
    transStateTime = QDateTime::currentDateTime().toTime_t();
}

bool CTransInfo::bTransOk()
{
    return transResult == Tr_Successed;
    //
}

void CTransInfo::operator=(const CTransInfo &TransInfo)
{
    bIsFree = TransInfo.bIsFree;
    this->dwOBUID = TransInfo.dwOBUID;
    OBUBaseInfo = TransInfo.OBUBaseInfo;
    OBUVehInfo = TransInfo.OBUVehInfo;
    VehInfo = TransInfo.VehInfo;
    IccInfo = TransInfo.IccInfo;
    vehEntryInfo = TransInfo.vehEntryInfo;
    m_bBadCard = TransInfo.m_bBadCard;
    RsuOpResult = TransInfo.RsuOpResult;
    ConsumeInfo = TransInfo.ConsumeInfo;
    transState = TransInfo.transState;
    transStateTime = TransInfo.transStateTime;

    transResult = TransInfo.transResult;
    curVehPos = TransInfo.curVehPos;
    curFrameId = TransInfo.curFrameId;
    bReGetTac = TransInfo.bReGetTac;
    m_bForTac = TransInfo.m_bForTac;
    nErrorCode = TransInfo.nErrorCode;
    m_sError = TransInfo.m_sError;
    nDetectNum = TransInfo.nDetectNum;
    tmDetectTime = TransInfo.tmDetectTime;
    // VehTollInfo = TransInfo.VehTollInfo;
    //   memcpy(&favResultInfo,&TransInfo.favResultInfo,sizeof favResultInfo);
    TransTime = TransInfo.TransTime;
    AutoRegInfo = TransInfo.AutoRegInfo;

    memcpy(&tradInfo, &TransInfo.tradInfo, sizeof tradInfo);
    memcpy(&feeInfo, &TransInfo.feeInfo, sizeof feeInfo);

    bHolidayFree = TransInfo.bHolidayFree;
    bManNum8 = TransInfo.bManNum8;
    memcpy(GBSpEvent, TransInfo.GBSpEvent, sizeof GBSpEvent);
    bInvoice = TransInfo.bInvoice;
    m_BeginTime = TransInfo.m_BeginTime;
    ConsumeMoney = TransInfo.ConsumeMoney;
    m_endTime = TransInfo.m_endTime;

    etcTransType = TransInfo.etcTransType;

    // bChargeMode = TransInfo.bChargeMode;

    MinFeeInfo = TransInfo.MinFeeInfo;

    feeBoardPlay = TransInfo.feeBoardPlay;
    actualFeeClass = TransInfo.actualFeeClass;

    nMinPerCent = TransInfo.nMinPerCent;
    gantryFeeInfo = TransInfo.gantryFeeInfo;
    // dwLastMoney   = TransInfo.dwLastMoney;
    memcpy(OncePaySpEvent, TransInfo.OncePaySpEvent, sizeof OncePaySpEvent);

    m_curGantryInfo = TransInfo.m_curGantryInfo;
    m_bOpenGantry = TransInfo.m_bOpenGantry;
    m_sVLPId = TransInfo.m_sVLPId;
    m_sVehicleSignId = TransInfo.m_sVehicleSignId;

    m_bReserved = TransInfo.m_bReserved;
    m_bWhiteVeh = TransInfo.m_bWhiteVeh;
    lSpeedStartTime = TransInfo.lSpeedStartTime;
    lSpeedStopTime = TransInfo.lSpeedStopTime;
    m_InQueTime = TransInfo.m_InQueTime;
    m_bEmVeh = TransInfo.m_bEmVeh;
    m_emVehInfo = TransInfo.m_emVehInfo;

    m_nTotalFee = TransInfo.m_nTotalFee;
    m_nTransFee = TransInfo.m_nTransFee;
    m_nProvFee = TransInfo.m_nProvFee;
    m_nDiscountFee = TransInfo.m_nDiscountFee;
    m_nCardCost = TransInfo.m_nCardCost;
    m_nDiscountType = TransInfo.m_nDiscountType;
    m_nProvinceDiscountFee = TransInfo.m_nProvinceDiscountFee;
    m_nOriginFee = TransInfo.m_nOriginFee;
    m_nCalcMaxFee = TransInfo.m_nCalcMaxFee;
    m_nFeePercent = TransInfo.m_nFeePercent;

    m_nPsamSerial = TransInfo.m_nPsamSerial;
    memcpy(m_TermCode, TransInfo.m_TermCode, sizeof m_TermCode);
    m_nRsuIndex = TransInfo.m_nRsuIndex;

    cpcIccInfo = TransInfo.cpcIccInfo;
    mediaType = TransInfo.mediaType;
    m_bVehState = TransInfo.m_bVehState;

    m_vehAxisInfo = TransInfo.m_vehAxisInfo;
    m_dwToTalWeight = TransInfo.m_dwToTalWeight;
    m_dwWeightLimit = TransInfo.m_dwWeightLimit;
    m_nAxisNum = TransInfo.m_nAxisNum;
    m_nOverRate = TransInfo.m_nOverRate;
    m_sCertNo = TransInfo.m_sCertNo;
    m_fareQryCondition = TransInfo.m_fareQryCondition;
    m_fareQryResult = TransInfo.m_fareQryResult;
    m_llInvoiceId = TransInfo.m_llInvoiceId;
    m_nInvoiceCnt = TransInfo.m_nInvoiceCnt;
    m_sListNo = TransInfo.m_sListNo;
    m_splainText = TransInfo.m_splainText;
    m_sQrCode = TransInfo.m_sQrCode;
    m_sOpId = TransInfo.m_sOpId;

    m_sPaperId = TransInfo.m_sPaperId;
    m_sPaperKey = TransInfo.m_sPaperKey;

    m_transPayType = TransInfo.m_transPayType;
    m_payType = TransInfo.m_payType;
    m_nTotalMiles = TransInfo.m_nTotalMiles;
    m_bGreenCheckFailed = TransInfo.m_bGreenCheckFailed;
    curFrameTime = TransInfo.curFrameTime;
    m_sId = TransInfo.m_sId;
    m_bCardMgrIndex = TransInfo.m_bCardMgrIndex;

    m_payCode = TransInfo.m_payCode;
    m_payOrderNum = TransInfo.m_payOrderNum;
    m_nPayNetType = TransInfo.m_nPayNetType;
    m_nPayChannel = TransInfo.m_nPayChannel;

    m_bBlackCard = TransInfo.m_bBlackCard;
    m_nOutTimes = TransInfo.m_nOutTimes;
    m_bRepay = TransInfo.m_bRepay;
    m_bUReturn = TransInfo.m_bUReturn;
    m_bReprint = TransInfo.m_bReprint;
    m_nPrepayList = TransInfo.m_nPrepayList;
    m_nRepayType = TransInfo.m_nRepayType;
    m_bNoCard = TransInfo.m_bNoCard;
    m_PayCardInfo = TransInfo.m_PayCardInfo;

    m_nProvinceCount = TransInfo.m_nProvinceCount;
    m_sTmpFileName = TransInfo.m_sTmpFileName;
    m_bPassPermit = TransInfo.m_bPassPermit;
    m_operInfo = TransInfo.m_operInfo;
    m_AuthTime = TransInfo.m_AuthTime;
    m_bEnterQueue = TransInfo.m_bEnterQueue;
    m_vcrResult = TransInfo.m_vcrResult;

    m_feeInfoL = TransInfo.m_feeInfoL;
    m_bDifferentFee = TransInfo.m_bDifferentFee;
    return;
}

void CTransInfo::SetDetectCnt(int nDetectCnt)
{
    nDetectNum = nDetectCnt;
    tmDetectTime = QDateTime::currentDateTime().toTime_t();
}

void CTransInfo::SetSaveResult(bool bRlt)
{
    if (bRlt) {
        transState = Ts_Finished;
    }
}

void CTransInfo::ClearTransInfo()
{
    bIsFree = false;
    bIsJC = false;
    dwOBUID = 0;
    OBUBaseInfo.Clear();
    OBUVehInfo.Clear();
    VehInfo.Clear();
    IccInfo.Clear();
    m_bBadCard = false;
    m_bNoCard = false;
    vehEntryInfo.Clear();
    RsuOpResult.Clear();
    ConsumeInfo.clear();
    transState = Ts_WaitTransBegin;
    transStateTime = 0;
    transResult = Tr_None;  //只有交易状态结束时才有意义
    curVehPos.Clear();
    curFrameId = 0;
    curFrameTime = 0;
    bReGetTac = false;
    m_sError.clear();
    m_bForTac = false;
    tmDetectTime = 0;
    this->nDetectNum = 0;
    m_BeginTime = 0;
    m_endTime = 0;
    AutoRegInfo.ClearResult();
    sCapImageFileName.clear();
    TransTime = QDateTime::currentDateTime();
    memset(&tradInfo, 0, sizeof tradInfo);
    memset(&feeInfo, 0, sizeof feeInfo);

    memset(GBSpEvent, '0', sizeof GBSpEvent);

    bHolidayFree = 0;
    bManNum8 = false;
    bInvoice = false;
    ConsumeMoney = 0;
    etcTransType = TransType_None;
    // bChargeMode = 1;
    feeBoardPlay = 2;
    actualFeeClass = FeeClass_None;
    MinFeeInfo.Clear();
    nMinPerCent = 0;
    gantryFeeInfo.Clear();
    // dwLastMoney =0;
    memset(OncePaySpEvent, 0, sizeof OncePaySpEvent);
    m_curGantryInfo.Clear();
    m_bOpenGantry = false;
    m_sVLPId.clear();
    m_sVehicleSignId.clear();

    m_nTotalFee = 0;
    m_nTransFee = 0;
    m_nProvFee = 0;
    m_bReserved = false;
    m_bWhiteVeh = false;

    lSpeedStartTime = 0;
    lSpeedStopTime = 0;
    m_InQueTime = 0;
    m_bEmVeh = false;
    m_emVehInfo.ClearEmVehInfo();

    m_nDiscountFee = 0;
    m_nCardCost = 0;
    m_nDiscountType = 0;
    m_nProvinceDiscountFee = 0;
    m_nOriginFee = 0;

    m_nPsamSerial = 0;
    m_nRsuIndex = 0;
    memset(m_TermCode, 0, sizeof m_TermCode);
    m_nCalcMaxFee = 0;
    m_nFeePercent = 0;

    m_bVehState = 0xff;
    mediaType = MediaType_None;
    cpcIccInfo.Clear();
    m_vehAxisInfo.Clear();
    m_dwToTalWeight = 0;
    m_dwWeightLimit = 0;
    m_nAxisNum = 0;
    m_sCertNo.clear();
    m_fareQryCondition.ClearCondition();
    m_fareQryResult.Clear();
    m_bWriteCard_Exit = false;
    m_llInvoiceId = 0;
    m_nInvoiceCnt = 0;
    m_sListNo.clear();
    m_splainText.clear();
    m_sQrCode.clear();
    m_sOpId.clear();
    m_sPaperId.clear();
    m_transPayType = TransPT_None;
    m_payType = PayType_None;

    m_nTotalMiles = 0;
    m_bGreenCheckFailed = false;
    m_sId.clear();
    m_bCardMgrIndex = 0;

    m_payCode.clear();
    m_payOrderNum.clear();
    m_nPayChannel = 0;
    m_nPayNetType = 0;

    m_bBlackCard = false;
    m_nOutTimes = 0;
    m_bRepay = false;
    m_bUReturn = false;
    m_bReprint = false;
    m_nPrepayList = 0;
    m_nRepayType = 0;
    m_nOverRate = 0;
    m_PayCardInfo.clear();

    m_nProvinceCount = 0;
    m_sTmpFileName.clear();
    m_bPassPermit = false;
    m_operInfo.Clear();
    m_bEnterQueue = false;
    m_sPaperKey.clear();
    m_vcrResult.ClearResult();
    memset(&m_feeInfoL, 0, sizeof m_feeInfoL);
    m_bDifferentFee = false;

    if (m_nRsuIndex == DevIndex_Manual) {
        //如果是人工业务，通知远控端清除业务信息
        Ptr_RemoteCtrl->ClearTransInfo();
    }

    return;
}

void CTransInfo::SetOBUBaseInfo(quint32 OBUID, const COBUBaseInfo *pOBUBaseInfo)
{
    dwOBUID = OBUID;
    if (pOBUBaseInfo) {
        memcpy(&OBUBaseInfo, pOBUBaseInfo, sizeof OBUBaseInfo);
        curVehPos = pOBUBaseInfo->vehPos;
        nErrorCode = pOBUBaseInfo->ErrorCode;
        curVehPos.pcFrameType = 0xB2;
        mediaType = MediaType_OBU;
    }
    curFrameId = 0xB2;
    m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    m_endTime = 0;
}

void CTransInfo::SetOBUVehInfo(const COBUVehInfo *pOBUVehInfo, const CVehInfo *pVehInfo)
{
    if (pOBUVehInfo) {
        memcpy(&OBUVehInfo, pOBUVehInfo, sizeof OBUVehInfo);
        curVehPos = pOBUVehInfo->vehPos;
        nErrorCode = pOBUVehInfo->ErrorCode;
        curVehPos.pcFrameType = 0xB3;
    }
    if (pVehInfo) VehInfo = *pVehInfo;
    curFrameId = 0xB3;
}

//入口货车重量，如果obu内重量<4500以obu为准 否则填 1500
void CTransInfo::SetVehInfo(const CVehInfo *pVehInfo, const CVehAxisInfo *pVehAxisInfo)
{
    if (pVehInfo) {
        VehInfo = *pVehInfo;
        m_bVehState = GetVehStateByVehType(VehInfo.GBVehType);
    }

    if (pVehAxisInfo) m_vehAxisInfo = *pVehAxisInfo;

    if (!pVehAxisInfo) {  //这种情况只允许客1或货1，因此轴数缺省为2
        m_vehAxisInfo.Clear();
        if (isTruck(VehInfo.VehClass) || isZhuangXiangTruck(VehInfo.VehClass)) {
            DebugLog(QString("无称重信息,货车填写缺省称重信息"));
            m_dwToTalWeight = 0xffffff;    // TotalWeight_Default;
            m_dwWeightLimit = 0xffffffff;  // TotalWeight_Limit;  // m_dwToTalWeight;
            m_nAxisNum = 0;                //货车没有称重信息，设置轴数为0
        } else {
            //客车默认填写0xFF
            DebugLog(QString("无称重信息,客车填写缺省称重信息"));
            m_dwToTalWeight = 0xffffff;  // obuVehInfo.dwTotalWeight;
            m_dwWeightLimit = 0xffffffff;
            m_nAxisNum = GetVehAxisNumByVC(VehInfo.VehClass);
        }

    } else {
        m_dwToTalWeight = pVehAxisInfo->GetConfirmedTotalRawWeight();
        m_dwWeightLimit = pVehAxisInfo->GetWeightStandard();
        if (isZhuangXiangTruck(VehInfo.VehClass)) {
            m_dwWeightLimit = pVehAxisInfo->GetWeightStandardForSpecial();
        }
        m_nAxisNum = pVehAxisInfo->GetConfirmedSingleAxisNum();
        if (isCar(VehInfo.VehClass)) {
            if (m_nAxisNum != 2) {
                m_nAxisNum = 2;
            }
        }
        if (0 == m_nAxisNum) {
            DebugLog(QString("transInfo保存称重信息,称重信息取出轴数为0"));
            m_nAxisNum = GetVehAxisNumByVC(VehInfo.VehClass);
            DebugLog(QString("transInfo保存称重信息,称重信息取出轴数为0,按车型取出轴数:%1")
                         .arg(m_nAxisNum));
        }
        DebugLog(QString("TransInfo 保存称重信息%1,%2,%3")
                     .arg(m_dwToTalWeight)
                     .arg(m_dwWeightLimit)
                     .arg(m_nAxisNum));
    }
    return;
}

void CTransInfo::SetAutoVehInfo(const CVehInfo *pAutoVehInfo)
{
    if (pAutoVehInfo) {
        VehInfo.AutoVehClass = pAutoVehInfo->AutoVehClass;
        VehInfo.nAutoVehPlateColor = pAutoVehInfo->nAutoVehPlateColor;
        memcpy(&VehInfo.szAutoVehPlate, pAutoVehInfo->szAutoVehPlate,
               sizeof VehInfo.szAutoVehPlate);
    }
    return;
}

void CTransInfo::SetCardTollInfo(const CCardTollInfo *pCardTollInfo, const QDateTime *pTransTime)
{
    if (pCardTollInfo) IccInfo.CardTollInfo = *pCardTollInfo;
    if (pTransTime) {
        TransTime = *pTransTime;
    }
}

void CTransInfo::SetIccInfo(const CRsuIccInfo *pIccInfo)
{
    if (pIccInfo) {
        IccInfo = *pIccInfo;
        nErrorCode = pIccInfo->ErrorCode;
    }
    curFrameId = 0xB4;
}

void CTransInfo::SetCPCIccInfo(const CCPCIccInfo *pIccInfo)
{
    if (pIccInfo) {
        cpcIccInfo = *pIccInfo;
        mediaType = MediaType_CPC;
    }
    return;
}

void CTransInfo::SetPaperInfo(quint64 lPaperNo, const QString &sKey)
{
    mediaType = MediaType_Paper;
    m_sPaperId = QString("%1").arg(lPaperNo);
    m_sPaperKey = sKey;
    return;
}

void CTransInfo::SetVehEntryInfo(const CVehEntryInfo &vehEnInfo, const QDateTime &ExitTime)
{
    vehEntryInfo = vehEnInfo;
    TransTime = ExitTime;
}

void CTransInfo::SaveTo(CTransInfo &TransInfo)
{
    TransInfo = *this;
    return;
}

bool CTransInfo::CheckRepeatTransByCard(const CRsuIccInfo *pIccInfo, int mSeconds)
{
    if (mSeconds > 0 && TransTime.isValid()) {
        int mSubSeconds = TransTime.msecsTo(QDateTime::currentDateTime());
        if (mSubSeconds > mSeconds) return false;
    }

    if (OBUBaseInfo.dwOBUID > 0 && transResult >= Tr_Successed) {
        if ((IccInfo.ProCardBasicInfo.bType == pIccInfo->ProCardBasicInfo.bType) &&
            (!qstrncmp(IccInfo.ProCardBasicInfo.szCardNo, pIccInfo->ProCardBasicInfo.szCardNo,
                       sizeof IccInfo.ProCardBasicInfo.szCardNo - 1))) {
            if (IccInfo.dwBalance - ConsumeMoney == pIccInfo->dwBalance) {
                return true;
            }
        }
    }
    return false;
}

/*
 *判断卡片是否重复交易、扣款
 *参数 pIccInfo -最新B帧数据上送的Ic卡信息
 *返回值：true - 已经扣款 false -未扣款。
 *判断标准：当前存在未交易完成的业务或交易失败的业务。当重新进行交易时，
 *如果新的卡片的与该未完成交易的卡片相同，且余额等于上次交易后的余额，则认为已经扣款，此时应重新获取卡片TAC码。
 * 判断重复扣款只处理正常业务。
 */

bool CTransInfo::CheckHasConsumeMoney(const CRsuIccInfo *pIccInfo)
{
    if (bWaitOpResult() || bTransFailed()) {
        if ((IccInfo.ProCardBasicInfo.bType == pIccInfo->ProCardBasicInfo.bType) &&
            (!qstrncmp(IccInfo.ProCardBasicInfo.szCardNo, pIccInfo->ProCardBasicInfo.szCardNo,
                       sizeof IccInfo.ProCardBasicInfo.szCardNo - 1))) {
            int seconds = TransTime.secsTo(QDateTime::currentDateTime());
            if (seconds > 15 * 60)  //超过15分钟则认为是重新驶入车辆。
            {
                return false;
            }
            if (IccInfo.dwBalance >= ConsumeMoney) {
                DebugLog(QString("卡片[%1]重复交易检查,扣款前余额[%2],扣款金额[%3],扣款后金额[%4]")
                             .arg(IccInfo.ProCardBasicInfo.szCardNo)
                             .arg(IccInfo.dwBalance)
                             .arg(ConsumeMoney)
                             .arg(pIccInfo->dwBalance));
                if (IccInfo.dwBalance - ConsumeMoney == pIccInfo->dwBalance) {
                    return true;
                }
            }
        }
    }
    return false;
}

/**
 * @brief 交易完成
 * @param pRsuOpResult -天线交易结果数据
 * @param bSuccessed   -交易结果 true-成功 false -失败
 * @param nRepeatTimes -交易重复次数 0-缺省。例 1-重复一次,总交易次数为2。
 * @return true-成功 false-失败
 */

void CTransInfo::CompleteTrans(int nDevIndex, CTransPayType transPayType,
                               const CRsuOpResult *pRsuOpResult, CTransResult bTransResult,
                               CTransState state)
{
    m_nRsuIndex = nDevIndex;
    transState = state;  // Ts_WaitToSave;
    transResult = bTransResult;
    m_payType = TransPayTypeToPayType(transPayType);
    m_transPayType = transPayType;
    if (mediaType == MediaType_OBU) {
        if ((transPayType != TransPT_OBU) && (transPayType != TransPT_ETCCard)) {
            m_transPayType = TransPT_ETCCard;
        }
    }

    if (pRsuOpResult)  // && (bTransResult>Tr_Failed))
    {
        memset(&ConsumeInfo, 0, sizeof ConsumeInfo);
        nErrorCode = pRsuOpResult->ErrorCode;
        curVehPos = pRsuOpResult->vehPos;
        curFrameId = 0xB5;

        RsuOpResult = *pRsuOpResult;
        FormatTimeToChar14(pRsuOpResult->PurchaseTime, ConsumeInfo.szConsumeTime);
        ConsumeInfo.dwMoney = ConsumeMoney;                              // 扣款金额(IN)
        memcpy(ConsumeInfo.psamTermNo, pRsuOpResult->RSUTerminalId, 6);  // PSAM卡终端机编号(OUT)
        ConsumeInfo.dwTermSeq = pRsuOpResult->PSAMPaySerial;  // 终端机交易序号(OUT)
        ConsumeInfo.bTransType = pRsuOpResult->TransType;     // 交易类型(OUT)
        ConsumeInfo.dwTac = pRsuOpResult->TAC;                // TAC(OUT)
        memcpy(ConsumeInfo.bTac, pRsuOpResult->bTac, sizeof pRsuOpResult->bTac);
        ConsumeInfo.wCardSeq = pRsuOpResult->CPUCardPaySerial;  // 用户卡交易序号(OUT)
        ConsumeInfo.bKeyVer = 0;

        ConsumeInfo.dwBalanceBefore = IccInfo.dwBalance;                // 扣款前余额(OUT)
        ConsumeInfo.dwBalanceAfter = IccInfo.dwBalance - ConsumeMoney;  // 扣款后余额(OUT)
        ConsumeInfo.KeyType = pRsuOpResult->KeyType;
        QString sTransTime = pRsuOpResult->PurchaseTime.toString("yyyy-MM-dd hh:mm:ss");
        QString sMsg =
            QString(
                "交易完成,交易时间:%1 交易金额:%2 终端机交易序列号: %3"
                "交易类型:%4 卡交易序列号: %5 Tac: %6 密钥标识 %7 扣款前余额 %8 扣款后余额 %9")
                .arg(sTransTime)
                .arg(ConsumeInfo.dwMoney)
                .arg(ConsumeInfo.dwTermSeq)
                .arg(ConsumeInfo.bTransType)
                .arg(ConsumeInfo.wCardSeq)
                .arg(ConsumeInfo.dwTac)
                .arg(ConsumeInfo.KeyType)
                .arg(ConsumeInfo.dwBalanceBefore)
                .arg(ConsumeInfo.dwBalanceAfter);
        DebugLog(sMsg);
        if (transPayType == TransPT_OBU) {
            m_PayCardInfo = IccInfo.ProCardBasicInfo;
        }
    } else {
    }

    m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    QString str = QTime::currentTime().toString("hhmmsszzz");

    if (transResult == Tr_Successed) {
        QString sBatch, sNo;
        if (Ptr_Info->IsExitLane()) {  //因为倒车可能导致流水号跳号，因此只有出口先赋值
            QString sLaneHex = m_curGantryInfo.sLaneHex;
            if (sLaneHex.isEmpty()) sLaneHex = Ptr_Info->GetHexLaneID();
            if (m_sId.isEmpty())
                m_sId = Ptr_Info->GetGBLaneId() +
                        CBatchMgr::GetBatchMgr()->GetBatchInfo(sBatch, sNo, sLaneHex,
                                                               CBatchMgr::SnType_Lane, false);
        }
    }
    if (etcTransType >= TransType_abNormal) {
        //异常写卡，已经在B4帧结果中提示逻辑失败,此处不处理。
    } else {
        QString sResult = transResult == Tr_Successed ? QString("1") : QString("3");
        if (MediaType_OBU == mediaType && dwOBUID > 0)
            StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_TradeStatus, str,
                       QString::fromAscii(OBUBaseInfo.szTime), sResult);
    }
    if (MediaType_OBU == mediaType && dwOBUID > 0)
        StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_OpMiliSecond, str,
                   QString::fromAscii(OBUBaseInfo.szTime), QString("%1ms").arg(GetTotalTime()));

    if (pRsuOpResult)
        StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_CardAfterBalance,
                   QString::fromAscii(OBUBaseInfo.szTime), str,
                   QString::number(ConsumeInfo.dwBalanceAfter));
    return;
}

void CTransInfo::SetConsumeInfo(const CProCardConsumeInfo &CardConsumeInfo)
{
    ConsumeInfo = CardConsumeInfo;
    // m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    transState = Ts_WaitToSave;
    transResult = Tr_Successed;
    m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    QString sMsg =
        QString(
            "读写器交易完成,交易时间:%1 交易金额:%2 终端机交易序列号: %3"
            "交易类型:%4 卡交易序列号: %5 Tac: %6 密钥版本号 %7 扣款前余额 %8 扣款后余额 %9")
            .arg(ConsumeInfo.szConsumeTime)
            .arg(ConsumeInfo.dwMoney)
            .arg(ConsumeInfo.dwTermSeq)
            .arg(ConsumeInfo.bTransType)
            .arg(ConsumeInfo.wCardSeq)
            .arg(ConsumeInfo.dwTac)
            .arg(ConsumeInfo.bKeyVer)
            .arg(ConsumeInfo.dwBalanceBefore)
            .arg(ConsumeInfo.dwBalanceAfter);
    DebugLog(sMsg);
}

void CTransInfo::SetPayCardInfo(const CProCardBasicInfo &proCardBasicInfo,
                                CProCardConsumeInfo &cardConsumeInfo)
{
    m_transPayType = TransPT_ETCCard;

    m_PayCardInfo = proCardBasicInfo;
    ConsumeInfo = cardConsumeInfo;
    ConsumeMoney = cardConsumeInfo.dwMoney;
    return;
}

void CTransInfo::SetTerminateCode(quint8 bTerminateCode[])
{
    memcpy(m_TermCode, bTerminateCode, 6);
}

QString CTransInfo::GetTransStateStr()
{
    switch (transState) {
        case Ts_WaitTransBegin:
            return QString("等交易开始");
        case Ts_WaitOBUVehInfo:
            return QString("等待车辆信息");
        case Ts_WaitIccInfo:
            return QString("等待卡片信息");
        case Ts_IsReadingIcc:
            return QString("正在进行读写卡");
        case Ts_WaitOpResult:
            return QString("等待交易结果");
        case Ts_WaitToSave:
            return QString("交易完成,车辆放行");
        case Ts_Finished:
            return QString("数据保存完毕");
        default:
            return QString("");
    }
}

qint64 CTransInfo::GetTotalTime()
{
    // DebugLog(QString("beginTime:%1,endTime:%2").arg(m_BeginTime).arg(m_endTime));
    qint64 totalTime = 0;
    if (m_BeginTime < m_endTime) {
        totalTime = m_endTime - m_BeginTime;
    } else {
        totalTime = 256;
    }

    if (totalTime < 0 || totalTime > 256) {
        totalTime = 256;
    }
    return totalTime;
}

void CTransInfo::FillDealStatus(char DealStatus[256])
{
    if (Ptr_Info->IsEntryLane()) {
        if (MediaType_OBU == mediaType) {
            DealStatus[PT_CTStoreCard] =
                IccInfo.ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD ? '1' : '0';
            DealStatus[PT_CTTallyCard] =
                IccInfo.ProCardBasicInfo.bType == CARD_TYPE_TALLY_CARD ? '1' : '0';
            DealStatus[PT_PStoreCard] = m_PayCardInfo.bType == CARD_TYPE_STORE_CARD ? '1' : '0';
            DealStatus[PT_PTallyCard] = m_PayCardInfo.bType == CARD_TYPE_TALLY_CARD ? '1' : '0';
        }

        if (isCar(VehInfo.VehClass)) {
            DealStatus[PT_VEHPASSPERMIT] = m_bPassPermit ? '1' : '0';
        }

    } else {
        if (MediaType_OBU == mediaType) {
            DealStatus[PT_CTStoreCard] =
                IccInfo.ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD ? '1' : '0';
            DealStatus[PT_CTTallyCard] =
                IccInfo.ProCardBasicInfo.bType == CARD_TYPE_TALLY_CARD ? '1' : '0';
        }
        switch ((int)m_payType) {
            case PayType_ETC: {
                DealStatus[PT_PStoreCard] = m_PayCardInfo.bType == CARD_TYPE_STORE_CARD ? '1' : '0';
                DealStatus[PT_PTallyCard] = m_PayCardInfo.bType == CARD_TYPE_TALLY_CARD ? '1' : '0';
                break;
            }
            case PayType_Cash: {
                DealStatus[PT_PCash] = '1';
                break;
            }
            case PayType_Union: {
                DealStatus[PT_PUniPayCard] = '1';
                break;
            }
            case PayType_Alipay:
            case PayType_WeChat:
            case PayType_Other: {
                if (m_nPayChannel > 0) {
                    if (MobilePayBase::PayChannel_TW == m_nPayChannel) {
                        DealStatus[PT_TW] = '1';
                    } else if (MobilePayBase::PayChannel_UnionPay == m_nPayChannel) {
                        DealStatus[PT_UnionPay] = '1';
                    } else if (MobilePayBase::PayChannel_CMB == m_nPayChannel) {
                        DealStatus[PT_CMB] = '1';
                    }
                }
                if (MobilePayBase::NetType_Inner == m_nPayNetType) {
                    DealStatus[PT_PAYNET_Inner] = '1';
                } else if (MobilePayBase::NetType_Tianyi == m_nPayNetType) {
                    DealStatus[PT_PAYNET_TianYi] = '1';
                } else if (MobilePayBase::NetType_AliYun == m_nPayNetType) {
                    DealStatus[PT_PAYNET_Ali] = '1';
                }
                break;
            }
        }

        if (!VehInfo.IsEmpty() && VehInfo.GBVehType == UVT_Normal) {
            DealStatus[PT_CGeneral] = '1';
        }

        if (isCar(VehInfo.VehClass)) {
            if (m_nDiscountFee + m_nProvinceDiscountFee > 0) DealStatus[PT_CYHKC] = '1';
        }

        DealStatus[PT_CGeneral] = VehInfo.GBVehType == UVT_Normal ? '1' : '0';
        DealStatus[PT_CArmy] = VehInfo.GBVehType == UVT_Army ? '1' : '0';
        DealStatus[PT_CJC] = VehInfo.GBVehType == UVT_Police ? '1' : '0';
        //   DealStatus[PT_CJC]     = VehInfo.VehType==VT_Police?'1':'0';
        DealStatus[PT_CJJ] = VehInfo.GBVehType == UVT_Emergency ? '1' : '0';
        DealStatus[PT_CFXJZ] = VehInfo.GBVehType == UVT_Rescue ? '1' : '0';
        if (m_bWhiteVeh) DealStatus[PT_CBMDGW] = '1';
        //   DealStatus[PT_CQMFGW]  = VehInfo.VehType==VT_AllFreeGongWu?'1':'0';
        //   DealStatus[PT_CLDMFGW] = VehInfo.VehType ==VT_PartFreeGongWu? '1':'0';
        DealStatus[PT_CQMLT] = VehInfo.GBVehType == UVT_FarmProduct ? '1' : '0';
        DealStatus[PT_CCD] = VehInfo.GBVehType == UVT_MotorCade ? '1' : '0';
    }

    if (VehInfo.VehClass > VC_Truck) {
        DealStatus[PT_SVT1] = '1';
    }

    // DealStatus[PT_VCT2]=VehInfo.VehClass>VC_Truck ?'1':'0';
    int nAxisNum = GetVehAxisNum();
    if (VehInfo.VehClass > VC_Truck) {
        DealStatus[PT_VCT2] = 2 == nAxisNum ? '1' : '0';
        DealStatus[PT_VCT3] = 3 == nAxisNum ? '1' : '0';
        DealStatus[PT_VCT4] = 4 == nAxisNum ? '1' : '0';
        DealStatus[PT_VCT5] = 5 == nAxisNum ? '1' : '0';
        DealStatus[PT_VCT6] = nAxisNum >= 6 ? '1' : '0';
    }
    if (isCar(VehInfo.VehClass)) {
        DealStatus[PT_VCM2] = '1';
    }

    if (m_bRepay) {
        DealStatus[PT_STBF4] = '1';
    }
    if (MediaType_Paper == mediaType) DealStatus[PT_SEZP] = '1';
    if (Ptr_Info->IsExitLane()) {
        if (m_bNoCard) DealStatus[PT_SENOCARD] = '1';
        if (m_bBadCard) DealStatus[PT_SEBADCARD2] = '1';
        if (OncePaySpEvent[OncePay_Sp_CardBalanceLow] == 1) DealStatus[PT_SEGTERR3] = '1';
        if (m_bUReturn) DealStatus[PT_SEUT1] = '1';
        if (m_nOutTimes > 0) DealStatus[PT_SEOVTIME] = '1';
        if (vehEntryInfo.nEntryType == Entry_ByCard) {
            QString sVLP = GB2312toUnicode(VehInfo.szVehPlate);
            QString sEnVLP = GB2312toUnicode(vehEntryInfo.szEnVLP);
            if (sVLP != sEnVLP || VehInfo.nVehPlateColor != vehEntryInfo.bEnVLPC) {
                DealStatus[PT_TSEPLATEERR] = '1';
            }
            if (isCar(VehInfo.VehClass) && isCar(vehEntryInfo.bEnVC)) {
                if (VehInfo.VehClass < vehEntryInfo.bEnVC) {
                    DealStatus[PT_TSEBDK1] = '1';
                } else if (VehInfo.VehClass > vehEntryInfo.bEnVC)
                    DealStatus[PT_TSEBDK2] = '1';
            }
        }
        if (m_bReprint) DealStatus[PT_TSEIVRPT] = '1';

        if (VehInfo.GBVehType != UVT_MotorCade && MediaType_None != mediaType) {
            if (bIsFree) DealStatus[PT_TSRSCM] = '1';
        }
        if (isCar(vehEntryInfo.bEnVC) && isTruck(VehInfo.VehClass)) {
            DealStatus[PT_TSRKCH] = '1';
        }
        if (isTruck(vehEntryInfo.bEnVC) && isCar(VehInfo.VehClass)) {
            DealStatus[PT_TSRHCK] = '1';
        }
        if (isTruck(vehEntryInfo.bEnVC) && isTruck(VehInfo.VehClass) &&
            VehInfo.GBVehType == UVT_Normal) {
            if (vehEntryInfo.bEnVC > VehInfo.VehClass)
                DealStatus[PT_TSHBTOS] = '1';
            else if (vehEntryInfo.bEnVC < VehInfo.VehClass)
                DealStatus[PT_TSHSTOB] = '1';
        }

        if (m_llInvoiceId > 0) DealStatus[PT_PRINTNOTE] = '1';

        if (Ptr_Info->bHaveCardMgr()) {
            switch (m_payType) {
                case PayType_Alipay:
                    DealStatus[PT_AutoPayAliPay] = '1';
                    break;
                case PayType_WeChat:
                    DealStatus[PT_AutoPayWeChat] = '1';
                    break;
                case PayType_Union:
                    DealStatus[PT_AutoPayUnionCard] = '1';
                    break;

                default:
                    break;
            }
        }

        if (bIsJC) DealStatus[PT_ChangBeiJiChang] = '1';

        DealStatus[PT_MANNUM89] = '0';
        if (bManNum8) {
            DealStatus[PT_MANNUM89] = '1';
        } else {
            if (dwOBUID > 0 && isCar(OBUVehInfo.bVehClass)) {
                if (8 == OBUVehInfo.dwManNum || 9 == OBUVehInfo.dwManNum) {
                    DealStatus[PT_MANNUM89] = '1';
                }
            }
        }

        if (vehEntryInfo.nEntryType == Entry_ByQry) {
            DealStatus[PT_QryEntry] = '1';
        } else if (vehEntryInfo.nEntryType == Entry_ByManual)
            DealStatus[PT_ManualEntry] = '1';

        if (1 == m_nPrepayList)
            DealStatus[PT_InListNoRepay] = '1';
        else if (2 == m_nPrepayList)
            DealStatus[PT_InListAndRepay] = '1';

        if (1 == m_nRepayType) {
            DealStatus[PT_STBF5] = '1';
        } else if (2 == m_nRepayType) {
            DealStatus[PT_STBF4] = '1';
        }
    }

    if (VehInfo.GBVehType == UVT_BigTruck) {
        DealStatus[PT_BIGVEH] = '1';
    }
    if (MediaType_CPC == mediaType) {
        DealStatus[PT_CTPassCard] = '1';
    }
    if (OncePaySpEvent[OncePay_Sp_CardInBList] == 1) DealStatus[PT_SEGTERR4] = '1';

    DealStatus[PT_SEUT2] = '0';  // VehTollInfo.bUx? '1':'0';
    DealStatus[PT_HOLIDAYFREE] = bHolidayFree ? '1' : '0';
    if (MediaType_None != mediaType)
        DealStatus[PT_YSNORMAL] = '1';
    else {
        if (m_bNoCard)
            DealStatus[PT_YSNORMAL] = '1';
        else
            DealStatus[PT_YSNORMAL] = '0';
    }

    //**************业务状态
    /*

     PT_YSNORMAL		= 119,		//实际发卡/收费业务	入出
     PT_YSWX			= 120,		//维修状态发卡/收费业务	入出
     PT_YSAPPON		= 121,		//车道启动
     PT_YSAPPOFF		= 122,		//车道关闭
     PT_YSLOGIN		= 123,		//车道上班
     PT_YSLOGOUT		= 124,		//车道下班
     */

    //   DealStatus[PT_GongAN]            = VehInfo.VehType ==VT_GongAN?'1':'0';
    DealStatus[PT_ComBineHarvester] = VehInfo.GBVehType == UVT_CombinHarvester ? '1' : '0';
    if (Ptr_Info->IsExitLane()) {
        DealStatus[PT_SPROADDISCOUNT] = '0';   // favResultInfo.dwDiscountType==3?'1':'0';
        DealStatus[PT_GANGKOU] = '0';          // favResultInfo.dwDiscountType==2?'1':'0';
        DealStatus[PT_JIZHANGSHENGJIE] = '0';  // favResultInfo.dwDiscountType==1?'1':'0';
        if (MediaType_CPC == mediaType && !m_bBadCard) {
            DealStatus[PT_CPCNoEF04] = 0 == cpcIccInfo.cpcTollCellInfo.nProvCnt ? '1' : '0';
            DealStatus[PT_CPCEF04Lost] = cpcIccInfo.cpcTollCellInfo.bLostTollCell ? '1' : '0';
            DealStatus[PT_CPCLostCell] = cpcIccInfo.cpcRoadInfo.bPassFlagCnt <= 1 ? '1' : '0';
        }
    }

    if (bInvoice) {
        DealStatus[PT_SECG1] = '1';
        DealStatus[PT_SECG3] = '1';
    }

    if (m_nCalcMaxFee > 0) {
        DealStatus[PT_CALCMAXFEE] = '1';
    }
    if (m_nFeePercent > 0) {
        DealStatus[PT_DynamicFeePercent] = '1';
    }
}
