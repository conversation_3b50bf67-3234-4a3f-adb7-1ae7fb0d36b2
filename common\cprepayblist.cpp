#include "cprepayblist.h"

#include <QSqlQuery>

CPrePayBList::CPrePayBList() { m_sConnectName = QString("prepayBList"); }

bool CPrePayBList::QueryVehInDB(const QString &sPlate, int nPlateColor, QString &sBlackType,
                                quint32 &oweMoney)
{
    DebugLog(QString("开始查找预缴费黑名单,%1").arg(sPlate));

    QMutexLocker lock(&m_dbMutex);
    if (!m_bLoaded) return false;

    m_pQuery->bindValue(":vehPlate", sPlate);
    m_pQuery->bindValue(":vlpColor", nPlateColor);

#define QT_DEBUG
//    m_pQuery->bindValue(":vehPlate", "赣A31225");
//    m_pQuery->bindValue(":vlpColor", nPlateColor);
#endif

    bool bRlt = m_pQuery->exec();
    if (bRlt && m_pQuery->next()) {
        sBlackType = m_pQuery->value(2).toString();
        oweMoney = m_pQuery->value(3).toUInt();
        DebugLog(
            QString("预追缴黑名单车辆,sBlackType:%1,oweMoney:%2").arg(sBlackType).arg(oweMoney));
        sBlackType = GetDesc(sBlackType);
        return true;
    }
    return false;
}

bool CPrePayBList::PrePareQuery()
{
    if (!m_bLoaded) return false;

    if (m_pQuery) delete m_pQuery;

    m_pQuery = new QSqlQuery(*m_pDB);
    QString sSql = QString(
        "select VehPlate,VehPlateColor,BlackType,oweFee from tbl_ParamInfo where "
        "VehPlate=:vehPlate and VehPlateColor=:vlpColor;");
    bool bRlt = m_pQuery->prepare(sSql);
    if (!bRlt) {
        ErrorLog(QString("prepare failed"));
        return false;
    } else
        return true;
}

QString CPrePayBList::GetDesc(QString &sBlackType)
{
    QStringList bTypes = sBlackType.split("|");
    QString sAllDesc;
    foreach (QString(sSubTyps), bTypes) {
        QStringList subTypes = sSubTyps.split("-");
        QString sSubDesc;
        foreach (QString sType, subTypes) {
            int nType = sType.toInt();
            QString sDesc;
            switch (nType) {
                case 1:
                    sDesc = QString("闯关");
                    break;
                case 2:
                    sDesc = QString("换卡");
                    break;
                case 3:
                    sDesc = QString("丢失卡");
                    break;
                case 4:
                    sDesc = QString("恶意U行");
                    break;
                case 5:
                    sDesc = QString("损坏设施逃逸");
                    break;
                case 6:
                    sDesc = QString("协查车辆");
                    break;
                case 7:
                    sDesc = QString("假军车");
                    break;
                case 8:
                    sDesc = QString("假冒优惠车");
                    break;
                case 9:
                    sDesc = QString("绿色通道黑名单");
                    break;
                case 10:
                    sDesc = QString("恶意大车小标");
                    break;
                case 11:
                    sDesc = QString("ETC长期欠费");
                    break;
                case 12:
                    sDesc = QString("ETC大额欠费");
                    break;
                case 13:
                    sDesc = QString("多次丢卡");
                    break;
                case 14:
                    sDesc = QString("超时停留");
                    break;
                case 15:
                    sDesc = QString("套牌车");
                    break;
                case 16:
                    sDesc = QString("多牌假牌车");
                    break;
                case 17:
                    sDesc = QString("非法换货");
                    break;
                case 18:
                    sDesc = QString("多次超载");
                    break;
                case 19:
                    sDesc = QString("稽查案底车辆");
                    break;
                case 20:
                    sDesc = QString("第三方拦截车");
                    break;
                default:
                    sDesc = QString("追缴黑名单车辆<%1>").arg(nType);
                    break;
            }
            if (!sDesc.isEmpty()) sSubDesc = sSubDesc + "," + sDesc;
        }
        sAllDesc += sSubDesc;
    }
    if (!sAllDesc.isEmpty()) sAllDesc.remove(0, 1);
    return sAllDesc;
}
