[2025-08-25 13:48:46.113] [tid 60620] [LKY_JT.cpp:83][I] InitDev:: lpszConnPara1 = ************:7200, lpszConnPara2 = 
[2025-08-25 13:48:46.399] [tid 60620] [LKY_JT.cpp:92][I] InitDev:: strIP = ************, strPort = 7200
[2025-08-25 13:48:46.399] [tid 60620] [LKY_JT.cpp:70][I] CreateDeviceObj:: iConnectType == 0 ,use dll type
[2025-08-25 13:48:46.400] [tid 60620] [cLKYImp_sc.cpp:347][I] cLKYImp_sc::ReadConfig:: dllPath: D:\vsProject\LKY_JT\Release\SCCKG.dll
[2025-08-25 13:48:46.400] [tid 60620] [cLKYImp_sc.cpp:184][I] cLKYImp_sc::LoadLib:: LoadLib: D:\vsProject\LKY_JT\Release\SCCKG.dll
[2025-08-25 13:48:46.401] [tid 60620] [cLKYImp_sc.cpp:186][I] cLKYImp_sc::LoadLib:: LoadLibrary
[2025-08-25 13:48:46.401] [tid 60620] [cLKYImp_sc.cpp:197][I] cLKYImp_sc::LoadLib:: LoadLibraryA(D:\vsProject\LKY_JT\Release\SCCKG.dll) failed, error code: 126, Error: Module not found (126)
Possible solutions:
- Check DLL file exists at specified path
- Verify all dependent DLLs are available

[2025-08-25 13:48:46.402] [tid 60620] [cLKYImp_sc.cpp:39][I] cLKYImp_sc::Init:: m_hDLL == NULL || m_pInit == NULL
[2025-08-25 13:48:46.402] [tid 60620] [LKY_JT.cpp:109][E] InitDev:: Failed to connect to device
[2025-08-25 13:49:02.807] [tid 60620] [LKY_JT.cpp:146][I] CloseDev:: begin.
[2025-08-25 13:49:02.807] [tid 60620] [cLKYImp_sc.cpp:50][I] cLKYImp_sc::UnInit:: DLL does not have this interface, so no operation is performed
[2025-08-25 13:49:02.808] [tid 60620] [LKY_JT.cpp:153][I] CloseDev:: finish.
