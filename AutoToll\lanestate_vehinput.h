#ifndef CLANESTATE_VEHINPUT_H
#define CLANESTATE_VEHINPUT_H
#include <QDateTime>

#include "abstractstate.h"
#include "cardreader.h"
#include "cbigvehlist.h"
#include "cgcardblacktable.h"
#include "cgreencarreservetabble.h"
#include "gantrybyplate.h"
#include "transinfo.h"
#include "speventmgr.h"
#include "cflaglisttable.h"
#include "formselectbigveh.h"

#define timerCardMgrTime 1800
/*
 * 车辆信息输入状态机
 *
 */

enum COperateType
{
    Operate_info,         //提示
    Operate_Pause,        //暂停，系统暂停，等待操作人员干预处理
    Operate_WaitConfirm,  //等待确认,该状态下，按赣通卡键，系统会继续处理
    Operate_Continue      //继续交易,该状态下，系统会自动进行后续处理

};

struct CVehPassPermitInfo
{
    QString vehPlate;
    QDateTime occurTime;
    COperInfo operInfo;
};

enum OtherXEventId
{
    XEvent_None,
    XEvent_ConfirmAxis,    //车辆轴信息确认
    XEvent_Truck1Over,     //货1超限
    XEvent_VehPassPermit,  //客车夜间行驶许可
    XEvent_U,              // uj型车
    XEvent_J,              // J
    XEvent_BigVehSelect    // 大件车选择

};

struct COtherXVehInfo
{
    qint32 nEventId;  //事件Id
    quint32 ObuId;
    quint8 bVehClass;
    int bVlpColor;
    QString sVehPlate;
    quint8 bVehType;
    int nAxisNum;
    quint8 bOpType;  // 1-暂停 2-等待放行 3-继续交易 0-提示
    bool bOpRlt;     //处理结果，确认返回true，取消返回false
    QString sCertNo;
    QString sTailePlate;
    int nTailVlpColor;
    QString sDetail;
    quint32 nAxleType;

public:
    void Clear()
    {
        ObuId = 0;
        bVehClass = VC_None;
        bVehType = UVT_Normal;
        bVlpColor = 0xff;
        sVehPlate.clear();
        bOpType = 0;
        bOpRlt = false;
        sCertNo.clear();
        sDetail.clear();
        nAxisNum = 2;
        nAxleType = 0;
    }
    COtherXVehInfo() { Clear(); }
    QString GetEventMsg()
    {
        switch (nEventId) {
            case XEvent_ConfirmAxis:
                return QString("轴数确认");
                break;
            case XEvent_Truck1Over: {
                return QString("货1超限");
                break;
            }
            case XEvent_VehPassPermit: {
                return QString("客车夜间许可");
                break;
            }
            case XEvent_U: {
                return QString("U 型车");
                break;
            }
            case XEvent_J: {
                return QString("J 型车");
                break;
            }
            case XEvent_BigVehSelect: {
                return QString("大件车选择");
                break;
            }
            default: {
                return QString("ETC 等待确认");
            } break;
        }
    }
};

struct CLastOBUInfo
{
    int nRsuIndex;
    quint32 dwOBUId;
    qint64 nPauseTime;

public:
    void ClearLastOBUInfo()
    {
        nRsuIndex = 0;
        dwOBUId = 0;
        nPauseTime = 0;
    }

    CLastOBUInfo() { ClearLastOBUInfo(); }
};

class CLaneState_VehInput : public CAbstractState
{
    Q_OBJECT

public:
    enum
    {
        opState_None,           //初始状态
        opState_IsWritingCard,  //正在读写卡片
        opState_WaitUpCard,     //等待出卡
        opState_WaitPickCard,   //等待取卡
        opState_WaitVehLeave,   //等待车辆离开
        opState_Reverse,
        opState_InputVehInfo
    };
    explicit CLaneState_VehInput(qint32 nStateId, QObject *Parent = NULL);
    virtual ~CLaneState_VehInput();
    QString GetOpStateName(int nOpState);

public:
    void Enter();
    void Leave();
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

    bool ProcessDIEvent(qint32 nDI, bool bStatus, QString &sError);
    void DisplayTransError_ETC(int nIndex, quint32 nErrorCode, const QString &sError,
                               const QString &sFDMsg);
    bool ProcessRsuEvent_New(int nIndex, quint32 OBUID, int nEvent, int &nErrorCode,
                             QString &sError);

    bool OnTransFinished(int nIndex, bool bResult, int nErrorCode, const QString &sError,
                         bool bReTrans = false, CTransInfo *pTransInfo = NULL);

    bool OnTransFinished_Manual(bool bResult, int nErrorCode, const QString &sError,
                                CTransInfo *pTransInfo);

    virtual bool ProcessOBUVehInfo_Light(int nIndex, quint32 OBUID, int &nErrorCode,
                                         QString &sError, QString &sFDMsg, bool &bRepeat);

    virtual bool ProcesssOBUBaseInfo_Light(int nIndex, quint32 OBUID, int &nErrorCode,
                                           QString &sError, QString &sFDMsg);
    //新增入口填写
    void FillCardTollInfo(CCardTollInfo &cardTollInfo, const QDateTime &transTime,
                          CTransInfo *pTransInfo);

    // bStop 是否发送天线停止交易指令,缺省设置为true B4帧交易完一定不能清除当前交易信息。
    virtual bool ProcessIccInfo_Entry(int nIndex, quint32 OBUID, int &nErrorCode, QString &sMsg,
                                      QString &sFDMsg, bool &bStop);
    virtual bool ProcessIccInfo_Exit(int nIndex, quint32 OBUID, int &nErrorCode, QString &sMsg,
                                     bool &bStop);

    bool GetOpenGantyInfo_New(int nVehClass, const QString &sVehPlate, int nPlateColor,
                              const CCardTollInfo *pCardTollInfo, COpenGantryInfo &openGantryInfo,
                              bool bETC = false);

    bool WriteCardAndEfo04File(int nIndex, quint32 OBUID, CETCTransType transType, qint32 dwMoney,
                               const QDateTime &TransTime, const CPro0019Raw_NewGB *pRaw0019,
                               const CPro0015Raw *pRaw0015, EF04Raw *pEf04Raw);

    bool ProcessB5FrameInfo_Entry(int nIndex, quint32 OBUID, int &nErrorCode, QString &sMsg);
    bool ProcessB5FrameInfo_Exit(int nIndex, quint32 OBUID, int &nErrorCode, QString &sMsg);

    bool ProcessB7FrameInfo(int nIndex, quint32 OBUID, int &nErrorCode, QString &sMsg);

    bool ProcessE2FrameInfo(int nIndex, quint32 OBUID, int &nErrorCode, QString &sMsg);

    //校验是否为本站调头车，或者出口本上驶入车
    bool CheckCardTollInfo(const CCardTollInfo &cardTollInfo, bool bEntry, int nErrorCode,
                           QString &sError);
    bool CheckCardHasTrans(const CCardTollInfo &cardTollInfo, bool bEntry);

    // void GetCardTollInfo(CTransInfo *pTransInfo,const QDateTime &TransTime,quint8 *pTermId);
    //填写入口车道的入口信息-入口
    void GetEntryInforEntry(const QDateTime &TransTime, const CCardTollInfo &cardTollInfo,
                            CTransInfo *pTransInfo, CVehEntryInfo &EntryInfo);

    bool VerifyProCardBasicInfo_Light(int nIndex, CTransInfo *pTransInfo,
                                      const CProCardBasicInfo &ProCardBasicInfo, bool bEmVeh,
                                      CVehInfo &vehInfo, qint32 &nErrorCode, QString &sError);
    bool VerifyAreaCode(CTransInfo *pTransInfo, const CProCardBasicInfo &ProCardBasicInfo,
                        quint16 &NetWorkId, CAreaCode &AreaCode, qint32 &nErrorCode,
                        QString &sError);

    bool CheckOBU_Valid(int nIndex, qint32 &nErrorCode, QString &sError);

    int CheckVehList(int nIndex, const QString &sVehPlate, qint32 nColor, qint32 &nErrorCode,
                     QString &sError);

    void CompleteTransForWhiteVeh(int nIndex, bool bSaveCurTransInfo = false);

    bool CheckAllowETCTrans(int nIndex, QString &sError, QString &sFDMsg);

    bool CheckOtherXVehInfo(qint32 nEventId, const CVehInfo &vehInfo, int &nOpType, bool &bRlt,
                            const CBigVehInfo *pBigVehInfo, int nAxisNum, QString &sDetail,
                            quint32 nAxleType = 0);

    bool CheckVCAndAxis(const CVehInfo &vehInfo, int nAxisNum, bool bYj, QString &sError,
                        QString &sFDMsg);

    bool CheckVCAndAxisNew(const CVehInfo &vehInfo, int nAxisNum, bool bYj, QString &sError, 
                         QString &sFDMsg, bool bEtcTrade = false);
    //远控端模拟落杆
    virtual void OnRemoteSimulateDownBar();

public slots:
    void OnSpEventVehMessageEvent(qint32 nXEventId, quint8 bVehType, int nPlateColor,
                                  const QString &sVehPlate, int nAxisNum, const QString &sCertNo,
                                  const QString &sDetail);

    void OnVehStayOutTimer();
    virtual void OnCardMgrTimer() = 0; // 卡机定时器触发的槽函数
    // 卡机定时器相关函数和变量
    virtual void StopCardMgrTimer(int type=0) = 0; // 停止卡机定时器
    virtual void StartCardMgrTimer() = 0; // 开始卡机定时器
    virtual void MoveMgrHead() = 0; // 移动卡头

signals:
    void NotifySpEventVehMessageEvent(qint32 nXEventId, quint8 bVehType, int nPlateColor,
                                      const QString &sPlate, int nAxisNum, const QString &sCertNo,
                                      const QString &sDetail);

protected:
    bool CheckRsuPause();
    //输入票号
    bool InputInvoiceNo();
    //输入车牌
    bool InputPlate();
    //设置输入车型
    void SetInputVehClass(CVehClass vehClass);
    //设置输入车种
    void SetInputVehType(CUnionVehType vehType);
    //设置输入车牌
    void SetInputVehPlate(const QString &sVehPlate, int nVLPColor);
    //设置输入轴型
    void SetInputAxleType(const QString &axleType);
    //清除输入
    void ResetInput();
    /*****************更新界面显示 ******************/
    //更新车型显示
    void RefreshVehClassShow();
    //更新车牌显示
    void RefreshVehPlateShow();
    //更新轴型显示
    void RefreshAxleTypeShow();
    //更新车种显示
    void RefreshVehTypeShow();
    //处理车型输入
    void ProcessKeyEvent_VehClassInput(MtcKeyPressedEvent *mtcKeyEvent);
    //处理车种输入
    void ProcessKeyEvent_VehTypeInput(MtcKeyPressedEvent *mtcKeyEvent);

    int ProcessKeyEvent_VehPlateInput(MtcKeyPressedEvent *mtcKeyEvent);

    int ProcessKeyEvent_GanTongKa();

    void VehClassInput_EscKeyProcess();

    void FD_DisplayTransInfo(int nIndex, CTransInfo *pTransInfo, bool bRetrans);

    bool GetCurVehWeightInfo(int vehClass, CVehAxisInfo &vehAxisInfo, quint32 &dwTotalWeight,
                             quint32 &dwLimitWeight, qint32 &nOverRate);
    bool QryTransShare(int nCurMediaType, const QString &sPlate, QString &sError);

    bool GetVehInfoForCPC(bool bCheckInput, CTransInfo *pTransInfo, CVehInfo &vehInfo,
                          CVehAxisInfo &vehAxisInfo, QString &sError, QString &sFDMsg);

    bool GetVehInfoForCPC_New(bool bCheckInput, CTransInfo *pTransInfo, CVehInfo &vehInfo,
                              CVehAxisInfo &vehAxisInfo, QString &sError, QString &sFDMsg);

    bool CheckAndRegetVehInfo(CVehInfo &vehInfo, VcrResult &vcrResult);

    /**
     * @brief 处理大件车多选择逻辑的辅助函数
     * @param sVehPlate 车牌号
     * @param nVlpColor 车牌颜色
     * @param bigVehInfo 输出参数，选中的大件车信息
     * @param bShowDialog 是否显示选择对话框，默认为true
     * @return true-找到并选择了大件车，false-未找到或取消选择
     */
    bool HandleBigVehSelection(const QString &sVehPlate, int nVlpColor, CBigVehInfo &bigVehInfo, bool bShowDialog = true);

    void DisplayTransError_CPC(int nIndex, const QString &sError, const QString &sFDMsg,
                               bool bAlarm);

    //此时车辆信息输入完成，判断是否打开读写器
    void CheckCardOperation();

    void ClearTransInfo_OnFrm();

    virtual bool ProcessKeyReprint();

    void DoOhterFreeVeh(CUnionVehType vehType);
    virtual bool DoHolidyFreeEntry();
    virtual bool DoQuanFanCheEntry();

    void FillAutoVLPInfo_FrontETC();

    void NotifyFrontRsuHasTrans();

    void UpdateVehInfo_First(CVehInfo &vehInfo, int nAxleNums);
    void UpdateVehInfo_ByEntryInfo(CVehInfo &vehInfo, int nEnVC);

    bool CheckAndRepassFrontETCVeh(const CTransInfo &transInfo);

protected:
    //出口
    bool GetEntryInfoFromCardTollInfo(const QDateTime &OccurTime, const CCardTollInfo &cardTollInfo,
                                      CVehEntryInfo &vehEntryInfo, qint32 &nErrorCode,
                                      QString &sError);

    bool QryMinFeeInfo(int nVehClass, const CVehEntryInfo &vehEntryInfo, bool bETC,
                       CAllRoadMinFeeInfo &minFeeInfo, int &nErrorCode, QString &sError);

    bool QryGreenVehInfo(const QString &sVehPlate, int nColor, CGreenCarInfo &greenInfo);

protected:
    void FuncMenuProcess();
    int DoSimulateMenu();
    bool HolidayFreeMenu(int nVehClass, bool &bManNum8);

    void ShowLog(const QString &sLog);
    void DebugEmvehInfo(const CEmVehInfo &emVehInfo);
    bool CheckOBUVehInfo(int nIndex, CTransInfo *pCurTransInfo, bool bJinJi, int &nErrorCode,
                         QString &sError, QString &sFDMsg);

    bool CheckLastOBUPause(quint32 dwOBUId);
    void PauseOBU(quint32 dwOBUId);

    //检查输入车型（识别）同车辆轴数是否相符。
    bool CheckInputVCAndAxisNum(int &nAxisNum, QString &sError);
    bool CheckAndSetOpState(quint8 bOpState, QString &sDesc);
    void SetOpState(quint8 bOpState);

    bool CheckVehEntryInfo(int nIndex, CTransInfo *pTransInfo, const CVehEntryInfo &vehEntryInfo,
                           QString &sError, QString &sFDMsg);

    bool CheckFeeClass_Etc(CTransInfo *pTransInfo, quint32 nTransFee, CFeeClass &feeClass,
                           bool bUReturn, bool IsLocalEntry, bool bFree, QString &sMsg);

    bool CheckFeeClass_Etc_New(CTransInfo *pTransInfo, quint32 nTransFee, CFeeClass &feeClass,
                               bool bUReturn, bool IsLocalEntry, bool bFree, QString &sMsg);

    bool CheckFeeClass_Last(CTransInfo *pTransInfo, quint32 nTransFee, CFeeClass &feeClass,
                            bool bUReturn, bool IsLocalEntry, bool bFree, QString &sMsg);

    bool QueryFlagInfo(CTransInfo *pTransInfo, bool bETC, CFlagListInfo &flagListInfo);
    bool ShowInformation_Help(const QString &sTitle, const QString &sMsg, const QString &sHelp,
                              bool bAlarm, const QString &sFDMsg,
                              int nEventId = CSpEventMgr::SpEvent_Other,
                              bool allowRemoteCtrl = false);

    void AddToPermitList(const QString &sVehPlate, const COperInfo &operInfo);
    bool CheckVehInPermitList(const QString &sVehPlate, CVehPassPermitInfo &permitInfo);
    bool CheckAndInitForDelay(int nIndex, quint32 OBUID, CTransInfo *pTransInfo, int nDevType);

    void StartVehStayOutTimer();
    void StopVehStayOutTimer();


    QTimer m_timerCardMgr; // 卡机定时器
    int m_nTimerCardMgrInterval2; // 后天线交易到B4帧时再次延时时间
    bool m_nTimerCardMgrInterval2Flag; // 后天线交易到B4帧时再次延时标志
    quint32 m_nDelayedOBUID; // 已经执行过延时的OBUID，防止同一车辆多次B4帧导致重复延时
protected:
    quint8 m_bFrameId;
    qint32 m_nErrorCode;
    CCardBListQryInfo m_CardBListQryInfos[2];
    CCardBListQryInfo m_CardBListIncQryInfos[2];
    int m_OpState;
    QMutex m_OtherXMt;
    COtherXVehInfo m_OtherXVehInfo;

    CLastOBUInfo m_lastOBUInfo;
    QMutex m_lastOBUMt;

    QMutex m_opStateMt;
    bool m_IsInputingVLP;

    // 暂存多个大件车查询结果，等待用户确认
    QList<CBigVehInfo> m_pendingBigVehList;

    //允许通行的车辆列表
    QMutex m_permitMt;
    QList<CVehPassPermitInfo> m_PermitVehList;

    QTimer m_vehStayOutTimer;
    QMutex m_vehStayOutMx;
};

#endif  // CLANESTATE_VEHINPUT_H
