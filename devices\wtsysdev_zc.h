#ifndef WTSYSDEV_ZC_H
#define WTSYSDEV_ZC_H

#include "abstractdev.h"
#include "pausablethread.h"
#include "weighttype.h"
#include "vehweightinfo.h"
#ifdef Q_OS_WIN32
#include <windows.h>
#endif
#ifdef Q_OS_UNIX
#define WINAPI
#endif

class CWtSysDev_ZC;

// thread---operator CIOCard
class CWtSysThread_ZC : public CPausableThread
{
    Q_OBJECT
public:
    CWtSysThread_ZC() : CPausableThread()
    {
        setObjectName(QString("WtSysDetectThread"));
        m_pWtDev = NULL;
        m_waitTime = 10;
    }
    void SetWtDev(CWtSysDev_ZC *pDev) { m_pWtDev = pDev; }

protected:
    virtual bool RunOnce(void);

protected:
    CWtSysDev_ZC *m_pWtDev;
};

class CWtSysDev_ZC : public CAbstractDev
{
    Q_OBJECT
public:
    CWtSysDev_ZC();
    virtual ~CWtSysDev_ZC();

    /**
     * @brief 设置驱动文件名
     * @param sDriver 驱动文件名
     */
    void SetDriver(const QString& sDriver);
    /**
     * @brief 设置连接字符串1
     * @param sConnStr1 串口号等信息
     */
    void SetConnStr1(const QString& sConnStr1);
    /**
     * @brief 设置连接字符串2
     * @param sConnStr2 波特率等信息
     */
    void SetConnStr2(const QString& sConnStr2);

protected:
    // 为防止重复加载动态库，此处使用静态变量
    static bool m_bDriverLoaded;
    static QLibrary m_hLibModule;
    //设备已完成串口已打开
    bool m_bDevInited;
    //工作线程对象
    CWtSysThread_ZC m_WorkThread;
    //
    int m_LastCount;
    int m_nWtStatus;

signals:
    // 称重错误信号
    void WeightError(const QString& sError);

public:
    virtual bool StartDev();
    virtual void CloseDev();
    virtual bool LoadDriver();
    virtual void ReleaseDriver();

public:
    //获取计重信息
    void GetWeightData();
    // 返回计重设备驱动是否已加载
    bool GetWimDriverLoaded() { return m_bDriverLoaded; }
    bool UploadVehWeightAgain();
    bool bUploadWeightAgain();
    
    // 使用入口称重信息的备用方案
    bool UseEntryWeightInfo(QString* pError);
    // 验证称重数据有效性
    bool ValidateWeightData(quint32 dwTotalWeight, int nAxisNum, QString* pError);
    // 分配轴组和重量
    bool DistributeWeightToAxleGroups(CVehAxisInfo &vehAxisInfo, quint32 dwTotalWeight, int nAxisNum, QString* pError);
    // 保存原始称重数据，用于回退
    void SaveOriginalWeightData(VehWeightInfo* pVehWeightInfo);
    // 从备份恢复称重数据（回退操作）
    bool RestoreWeightData();
    // 通知用户称重错误
    void NotifyWeightError(const QString& sError);
    
    /**
     * @brief 邯郸专用手动称重接口
     * @return bool 执行结果
     */
    bool DevFinish_HD();

    void GetVehicleOutlineInfo(CVehAxisInfo& vehAxisInfo);

protected:
    // DevFinish接口函数指针定义
    typedef int(WINAPI *Func_WtSys_DevFinish)();
    
    // WtSys_Init函数指针定义
    typedef int(WINAPI *Func_WtSys_Init)(int InitType);
    // WtSys_Test函数指针定义
    typedef int(WINAPI *Func_WtSys_Test)();
    // WtSys_SetCom函数指针定义
    typedef bool(WINAPI *Func_WtSys_SetCom)(char *iComID, int bps);
    // WtSys_CloseCom函数指针定义
    typedef bool(WINAPI *Func_WtSys_CloseCom)();
    // WtSys_ClearOne函数指针定义
    typedef bool(WINAPI *Func_WtSys_ClearOne)();
    // WtSys_GetVehicleCount函数指针定义
    typedef int(WINAPI *Func_WtSys_GetVehicleCount)();
    // WtSys_GetAxisCount函数指针定义
    typedef int(WINAPI *Func_WtSys_GetAxisCount)(int VehicleID);
    // WtSys_GetAxisData函数指针定义
    typedef int(WINAPI *Func_WtSys_GetAxisData)(int VehicleID, int AxisID, int *AxisType, long *Weight, int *AxisSpeed, int *MeterVer);
    // WtSys_ManualFinishing函数指针定义
    typedef int(WINAPI *Func_WtSys_ManualFinishing)();
    // WtSys_OpenManual函数指针定义
    typedef int(WINAPI *Func_WtSys_OpenManual)();
    // WtSys_CloseManual函数指针定义
    typedef int(WINAPI *Func_WtSys_CloseManual)();
    // WtSys_IsVehicleAlone函数指针定义
    typedef bool(WINAPI *Func_WtSys_IsVehicleAlone)(int nCount);
    // WtSys_UploadVehicleAgain函数指针定义
    typedef int(WINAPI *Func_WtSys_UploadVehicleAgain)();
    // WtSys_GetDevParameter函数指针定义
    typedef int(WINAPI *Func_WtSys_GetDevParameter)(int *Span, int *Incr, int *StartClearLmt, int *ManualClearLmt, int *TracertZeroLmt, int *TracertZeroSpd, int *SpanValue, int *ZeroValue, int *AxisValue, int *ReviseValue, int *MeterVer, int *SaveTime);
    // WtSys_GetFactoryInfo函数指针定义
    typedef int(WINAPI *Func_WtSys_GetFactoryInfo)(char *Manufacturer, char *ProductType, char *ManufactureDate, char *ProductSoftVer);

    // 函数指针变量
    Func_WtSys_Init WtSys_Init;
    Func_WtSys_Test WtSys_Test;
    Func_WtSys_SetCom WtSys_SetCom;
    Func_WtSys_CloseCom WtSys_CloseCom;
    Func_WtSys_ClearOne WtSys_ClearOne;
    Func_WtSys_GetVehicleCount WtSys_GetVehicleCount;
    Func_WtSys_GetAxisCount WtSys_GetAxisCount;
    Func_WtSys_GetAxisData WtSys_GetAxisData;
    Func_WtSys_ManualFinishing WtSys_ManualFinishing;
    Func_WtSys_OpenManual WtSys_OpenManual;
    Func_WtSys_CloseManual WtSys_CloseManual;
    Func_WtSys_IsVehicleAlone WtSys_IsVehicleAlone;
    Func_WtSys_UploadVehicleAgain WtSys_UploadVehicleAgain;
    Func_WtSys_GetDevParameter WtSys_GetDevParameter;
    Func_WtSys_GetFactoryInfo WtSys_GetFactoryInfo;
    
    // DevFinish函数指针
    Func_WtSys_DevFinish m_wtSysDevFinish;
    
    // 标记是否存在DevFinish接口
    bool m_hasDevFinish;
};

#endif // WTSYSDEV_ZC_H 
