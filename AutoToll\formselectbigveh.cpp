#include "formselectbigveh.h"
#include "globalui.h"
#include "MtcKey/MtcKeyDef.h"
#include "dlgmain.h"
#include "paramfilemgr.h"
#include "messagebox.h"
#include "laneinfo.h"
#include <QDateTime>
#include <QApplication>

FormSelectBigVeh::FormSelectBigVeh(QWidget *parent) : CBaseOpWidget(parent)
    , m_pTableWidget(NULL)
    , m_pBtnConfirm(NULL)
    , m_pBtnCancel(NULL)
    , m_pLblTitle(NULL)
    , m_pLblInstruction(NULL)
    , m_pLblDetailTitle(NULL)
    , m_pDetailScrollArea(NULL)
    , m_pDetailWidget(NULL)
    , m_pLblStationInfo(NULL)
    , m_pLblRouteInfo(NULL)
    , m_pLblTimeInfo(NULL)
    , m_pLblProvinceInfo(NULL)
    , m_pLblVehicleInfo(NULL)
    , m_pLblGoodsInfo(NULL)
    , m_nSelectedIndex(-1)
    , m_bConfirmed(false)
    , m_pOrgCodeTable(NULL)
    , m_pAreaCodeTable(NULL)
{
    setObjectName("FormSelectBigVeh");

    
    // 获取机构代码表实例
    m_pOrgCodeTable = dynamic_cast<COrgCodeTable*>(CParamFileMgr::GetParamFile(cfOrgCode));
    
    // 获取区域代码表实例
    m_pAreaCodeTable = dynamic_cast<CAreaCodeTable*>(CParamFileMgr::GetParamFile(cfAreaCode));
}

FormSelectBigVeh::~FormSelectBigVeh()
{
    // Qt会自动清理子控件，但为了确保安全，手动删除
    if (m_pTableWidget) {
        m_pTableWidget->deleteLater();
        m_pTableWidget = NULL;
    }
    if (m_pBtnConfirm) {
        m_pBtnConfirm->deleteLater();
        m_pBtnConfirm = NULL;
    }
    if (m_pBtnCancel) {
        m_pBtnCancel->deleteLater();
        m_pBtnCancel = NULL;
    }
    if (m_pLblTitle) {
        m_pLblTitle->deleteLater();
        m_pLblTitle = NULL;
    }
    if (m_pLblInstruction) {
        m_pLblInstruction->deleteLater();
        m_pLblInstruction = NULL;
    }
    
    // 清理详细信息相关控件
    if (m_pLblDetailTitle) {
        m_pLblDetailTitle->deleteLater();
        m_pLblDetailTitle = NULL;
    }
    if (m_pDetailScrollArea) {
        m_pDetailScrollArea->deleteLater();
        m_pDetailScrollArea = NULL;
    }
    if (m_pDetailWidget) {
        m_pDetailWidget->deleteLater();
        m_pDetailWidget = NULL;
    }
    if (m_pLblStationInfo) {
        m_pLblStationInfo->deleteLater();
        m_pLblStationInfo = NULL;
    }
    if (m_pLblRouteInfo) {
        m_pLblRouteInfo->deleteLater();
        m_pLblRouteInfo = NULL;
    }
    if (m_pLblTimeInfo) {
        m_pLblTimeInfo->deleteLater();
        m_pLblTimeInfo = NULL;
    }
    if (m_pLblProvinceInfo) {
        m_pLblProvinceInfo->deleteLater();
        m_pLblProvinceInfo = NULL;
    }
    if (m_pLblVehicleInfo) {
        m_pLblVehicleInfo->deleteLater();
        m_pLblVehicleInfo = NULL;
    }
    if (m_pLblGoodsInfo) {
        m_pLblGoodsInfo->deleteLater();
        m_pLblGoodsInfo = NULL;
    }
}

bool FormSelectBigVeh::ShowSelectDialog(const QList<CBigVehInfo> &bigVehList, CBigVehInfo &selectedInfo)
{
    // 如果列表为空，直接返回false
    if (bigVehList.isEmpty()) {
        return false;
    }
    
    // 保存大件车列表
    m_bigVehList = bigVehList;
    m_nSelectedIndex = 0;  // 默认选中第一项
    m_bConfirmed = false;
    
    // 初始化界面
    InitUI();
    
    // 设置表格数据
    SetTableData(m_bigVehList);
    
    // 显示对话框（模态）
    int result = doModalShow();
    
    // 如果用户确认选择且有有效的选择索引
    if (m_bConfirmed && m_nSelectedIndex >= 0 && m_nSelectedIndex < m_bigVehList.size()) {
        selectedInfo = m_bigVehList[m_nSelectedIndex];
        return true;
    }
    
    return false;
}

void FormSelectBigVeh::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    // 设置窗口标题
    SetTitle("大件车选择");
    
    // 使用全局UI配置的字体和尺寸，参照原有大件车输入界面
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    QFont fontText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    QFont fontEdit = QFont(g_GlobalUI.m_FontName, g_GlobalUI.bigtruck_EditFontSize);
    
    // 计算布局尺寸
    int padding = 10;
    int titleHeight = g_GlobalUI.optw_TitleHeight;
    int buttonHeight = 40;
    int instructionHeight = 25;
    int bottomSpace = buttonHeight + instructionHeight + padding * 3;
    

    // 创建表格控件
    if (!m_pTableWidget) {
        m_pTableWidget = new QTableWidget(this);
        connect(m_pTableWidget, SIGNAL(currentCellChanged(int,int,int,int)), 
                this, SLOT(OnTableCurrentChanged(int,int,int,int)));
    }
    
    // 表格位置：调整高度，显示3行记录
    int tableTop = titleHeight + padding;  // 直接从titleHeight开始，因为标题已隐藏
    int tableHeight = 120;  // 固定高度，约3行记录的高度（表头+3行数据）
    QRect tableRect(padding, tableTop, rect().width() - 2 * padding, tableHeight);
    m_pTableWidget->setGeometry(tableRect);
    
    // 设置表格列（简化显示）
    QStringList headers;
    headers << "序号" << "证书号" << "牵引车牌" << "挂车车牌";
    m_pTableWidget->setColumnCount(headers.size());
    m_pTableWidget->setHorizontalHeaderLabels(headers);
    
    // 设置表格样式
    SetTableStyle();
    

    // 创建详细信息滚动区域
    if (!m_pDetailScrollArea) {
        m_pDetailScrollArea = new QScrollArea(this);
        m_pDetailScrollArea->setWidgetResizable(true);
        m_pDetailScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        m_pDetailScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    }
    
    // 创建详细信息容器
    if (!m_pDetailWidget) {
        m_pDetailWidget = new QWidget();
        m_pDetailScrollArea->setWidget(m_pDetailWidget);
    }
    
    // 计算详细信息区域的位置和大小
    int detailAreaTop = tableRect.bottom() + padding;
    int detailAreaHeight = rect().height() - detailAreaTop - instructionHeight - padding * 2;
    QRect detailAreaRect(padding, detailAreaTop, rect().width() - 2 * padding, detailAreaHeight);
    m_pDetailScrollArea->setGeometry(detailAreaRect);
    
    // 创建详细信息标签
    QFont fontDetailText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize - 3);
    int labelHeight = 18;
    int labelSpacing = 2;
    int currentY = 5;
    
    if (!m_pLblStationInfo) {
        m_pLblStationInfo = new QLabel(m_pDetailWidget);
        m_pLblStationInfo->setFont(fontDetailText);
        m_pLblStationInfo->setWordWrap(true);
        m_pLblStationInfo->setGeometry(5, currentY, detailAreaRect.width() - 30, labelHeight);
        currentY += labelHeight + labelSpacing;
    }
    
    if (!m_pLblRouteInfo) {
        m_pLblRouteInfo = new QLabel(m_pDetailWidget);
        m_pLblRouteInfo->setFont(fontDetailText);
        m_pLblRouteInfo->setWordWrap(true);
        m_pLblRouteInfo->setGeometry(5, currentY, detailAreaRect.width() - 30, labelHeight);
        currentY += labelHeight + labelSpacing;
    }
    
    if (!m_pLblTimeInfo) {
        m_pLblTimeInfo = new QLabel(m_pDetailWidget);
        m_pLblTimeInfo->setFont(fontDetailText);
        m_pLblTimeInfo->setWordWrap(true);
        m_pLblTimeInfo->setGeometry(5, currentY, detailAreaRect.width() - 30, labelHeight);
        currentY += labelHeight + labelSpacing;
    }
    
    if (!m_pLblProvinceInfo) {
        m_pLblProvinceInfo = new QLabel(m_pDetailWidget);
        m_pLblProvinceInfo->setFont(fontDetailText);
        m_pLblProvinceInfo->setWordWrap(true);
        m_pLblProvinceInfo->setGeometry(5, currentY, detailAreaRect.width() - 30, labelHeight);
        currentY += labelHeight + labelSpacing;
    }
    
    if (!m_pLblVehicleInfo) {
        m_pLblVehicleInfo = new QLabel(m_pDetailWidget);
        m_pLblVehicleInfo->setFont(fontDetailText);
        m_pLblVehicleInfo->setWordWrap(true);
        m_pLblVehicleInfo->setGeometry(5, currentY, detailAreaRect.width() - 30, labelHeight);
        currentY += labelHeight + labelSpacing;
    }
    
    if (!m_pLblGoodsInfo) {
        m_pLblGoodsInfo = new QLabel(m_pDetailWidget);
        m_pLblGoodsInfo->setFont(fontDetailText);
        m_pLblGoodsInfo->setWordWrap(true);
        m_pLblGoodsInfo->setGeometry(5, currentY, detailAreaRect.width() - 30, labelHeight);
        currentY += labelHeight + labelSpacing;
    }
    
    // 设置详细信息容器的最小高度
    m_pDetailWidget->setMinimumHeight(currentY + 10);
    
    // 创建操作说明标签
    if (!m_pLblInstruction) {
        m_pLblInstruction = new QLabel(this);
    }
    m_pLblInstruction->setText("按【↑】【↓】键选择记录，按【确认】键确认，按【ESC】键返回");
    m_pLblInstruction->setAlignment(Qt::AlignCenter);
    m_pLblInstruction->setFont(fontText);
    QRect instructionRect(padding, rect().height() - instructionHeight - padding, rect().width() - 2 * padding, instructionHeight);
    m_pLblInstruction->setGeometry(instructionRect);
    
    if (m_pBtnConfirm) {
        m_pBtnConfirm->setVisible(false);
    }
    if (m_pBtnCancel) {
        m_pBtnCancel->setVisible(false);
    }

    filterChildrenKeyEvent();
}

int FormSelectBigVeh::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) {
        return 0;
    }
    
    // 数字键 - 直接选择对应序号的记录
    if (mtcKeyEvent->isNumKey()) {
        mtcKeyEvent->setKeyType(KC_Number);
        int index = mtcKeyEvent->ascii() - '1';  // 数字键从1开始，索引从0开始
        if (index >= 0 && index < m_bigVehList.size()) {
            m_nSelectedIndex = index;
            m_pTableWidget->setCurrentCell(m_nSelectedIndex, 0);
        }
        return 1;
    }
    
    // 功能键处理
    if (mtcKeyEvent->isFuncKey()) {
        mtcKeyEvent->setKeyType(KC_Func);
        int nFuncKey = mtcKeyEvent->func();
        
        switch (nFuncKey) {
            case KeyUp:
                // 上箭头
                if (m_nSelectedIndex > 0) {
                    m_nSelectedIndex--;
                    m_pTableWidget->setCurrentCell(m_nSelectedIndex, 0);
                }
                return 1;
                
            case KeyDown:
                // 下箭头
                if (m_nSelectedIndex < m_bigVehList.size() - 1) {
                    m_nSelectedIndex++;
                    m_pTableWidget->setCurrentCell(m_nSelectedIndex, 0);
                }
                return 1;
                
            case KeyConfirm:
                // 确定
                OnConfirm();
                return 1;
                
            case KeyEsc:
                // 取消
                OnCancel();
                return 1;
                
            default:
                break;
        }
    }
    
    return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
}

void FormSelectBigVeh::OnConfirm()
{
    if (m_nSelectedIndex >= 0 && m_nSelectedIndex < m_bigVehList.size()) {
        // 获取当前选中的大件车信息
        const CBigVehInfo &selectedInfo = m_bigVehList[m_nSelectedIndex];
        
        // 获取当前收费站的国标ID
        QString currentStationId = (Ptr_Info->GetGBStationId());
        
        // 根据当前收费站类型检查站点ID是否匹配
        bool needConfirm = false;
        
        if (Ptr_Info->IsEntryLane()) {
            // 对比入口站ID
            if (!selectedInfo.enStationIds.isEmpty()) {
                bool found = false;
                foreach (const QString &enStationIdStr, selectedInfo.enStationIds) {
                    if (!enStationIdStr.isEmpty()) {
                        // 按"|"分割站点ID字符串
                        QStringList stationIds = enStationIdStr.split("|", QString::SkipEmptyParts);
                        foreach (const QString &stationId, stationIds) {
                            if (stationId.trimmed() == currentStationId) {
                                found = true;
                                break;
                            }
                        }
                        if (found) break;
                    }
                }
                if (!found) {
                    needConfirm = true;
                }
            }
        } else if (Ptr_Info->IsExitLane()) {
            //对比出口站ID
            if (!selectedInfo.exStationIds.isEmpty()) {
                bool found = false;
                foreach (const QString &exStationIdStr, selectedInfo.exStationIds) {
                    if (!exStationIdStr.isEmpty()) {
                        // 按"|"分割站点ID字符串
                        QStringList stationIds = exStationIdStr.split("|", QString::SkipEmptyParts);
                        foreach (const QString &stationId, stationIds) {
                            if (stationId.trimmed() == currentStationId) {
                                found = true;
                                break;
                            }
                        }
                        if (found) break;
                    }
                }
                if (!found) {
                    needConfirm = true;
                }
            }
        }
        
        // 如果站点ID不匹配，弹出提示框
        if (needConfirm) {
            bool userConfirmed = CMessageBox::Information_Help(
                "站点不匹配",
                "大件车通行站点与当前收费站不匹配!",
                "按【确定】键继续",0,
                this,false
            );
            
            if (!userConfirmed) {
                // 返回主界面
                OnCancel();
                return;
            }
        }
        
        // 继续原有逻辑
        m_bConfirmed = true;
        setModalResult(Rlt_OK);
    }
}

void FormSelectBigVeh::OnCancel()
{
    m_bConfirmed = false;
    setModalResult(Rlt_Cancel);
}

void FormSelectBigVeh::OnTableCurrentChanged(int currentRow, int currentColumn, int previousRow, int previousColumn)
{
    Q_UNUSED(currentColumn);
    Q_UNUSED(previousRow);
    Q_UNUSED(previousColumn);
    
    // 更新选中索引
    m_nSelectedIndex = currentRow;
    
    // 更新详细信息显示
    if (currentRow >= 0 && currentRow < m_bigVehList.size()) {
        UpdateDetailInfo(m_bigVehList[currentRow]);
    }
}

void FormSelectBigVeh::SetTableData(const QList<CBigVehInfo> &bigVehList)
{
    if (!m_pTableWidget) {
        return;
    }
    
    // 设置行数
    m_pTableWidget->setRowCount(bigVehList.size());
    
    // 填充数据
    for (int i = 0; i < bigVehList.size(); i++) {
        const CBigVehInfo &info = bigVehList[i];
        
        // 序号
        QTableWidgetItem *itemIndex = new QTableWidgetItem(QString::number(i + 1));
        itemIndex->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 0, itemIndex);
        
        // 证书号
        QTableWidgetItem *itemCerNo = new QTableWidgetItem(info.cerNo);
        itemCerNo->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 1, itemCerNo);
        
        // 牵引车牌（包含颜色）
        QString tractorPlateText = info.tractor_vehicle_vlp;
        if (info.tractor_vehicle_vlpc > 0) {
            tractorPlateText += QString(" (%1)").arg(FormatVehicleColor(info.tractor_vehicle_vlpc));
        }
        QTableWidgetItem *itemTractorPlate = new QTableWidgetItem(tractorPlateText);
        itemTractorPlate->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 2, itemTractorPlate);
        
        // 挂车车牌（包含颜色）
        QString trailerPlateText = info.trailer_vehicle_vlp;
        if (info.trailer_vehicle_vlpc > 0) {
            trailerPlateText += QString(" (%1)").arg(FormatVehicleColor(info.trailer_vehicle_vlpc));
        }
        QTableWidgetItem *itemTrailerPlate = new QTableWidgetItem(trailerPlateText);
        itemTrailerPlate->setTextAlignment(Qt::AlignCenter);
        m_pTableWidget->setItem(i, 3, itemTrailerPlate);
    }
    
    // 选中第一行并更新详细信息
    if (bigVehList.size() > 0) {
        m_pTableWidget->setCurrentCell(0, 0);
        m_nSelectedIndex = 0;
        UpdateDetailInfo(bigVehList[0]);
    }
    
    // 调整列宽为自动适应内容
    m_pTableWidget->resizeColumnsToContents();
}

CBigVehInfo FormSelectBigVeh::GetSelectedBigVehInfo()
{
    if (m_nSelectedIndex >= 0 && m_nSelectedIndex < m_bigVehList.size()) {
        return m_bigVehList[m_nSelectedIndex];
    }
    
    return CBigVehInfo();  // 返回空的大件车信息
}

void FormSelectBigVeh::SetTableStyle()
{
    if (!m_pTableWidget) {
        return;
    }
    
    // 使用全局UI配置的字体
    int smallerFontSize = g_GlobalUI.optw_TextFontSize - 2;
    QFont headerFont = QFont(g_GlobalUI.m_FontName, smallerFontSize, QFont::Bold);
    QFont tableFont = QFont(g_GlobalUI.m_FontName, smallerFontSize);
    
    // 设置表格样式
    m_pTableWidget->setAlternatingRowColors(true);  // 交替行颜色
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);  // 选择整行
    m_pTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);   // 单选模式
    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);    // 禁止编辑
    
    // 设置表头样式
    QHeaderView *header = m_pTableWidget->horizontalHeader();
    if (header) {
        header->setResizeMode(QHeaderView::ResizeToContents);  // 列宽自动适应内容
        header->setFont(headerFont);
    }
    
    // 设置垂直表头
    QHeaderView *vHeader = m_pTableWidget->verticalHeader();
    if (vHeader) {
        vHeader->setVisible(false);  // 隐藏行号
    }
    
    // 设置表格字体
    m_pTableWidget->setFont(tableFont);
    
    // 设置行高
    m_pTableWidget->verticalHeader()->setDefaultSectionSize(30);
    
    // 设置表格边框
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setGridStyle(Qt::SolidLine);
    
    // 设置表格背景色
    m_pTableWidget->setStyleSheet(
        "QTableWidget {"
        "    background-color: white;"
        "    border: 2px solid gray;"
        "    selection-background-color: lightblue;"
        "}"
        "QTableWidget::item {"
        "    border-bottom: 1px solid lightgray;"
        "    padding: 5px;"
        "}"
        "QTableWidget::item:selected {"
        "    background-color: #4A90E2;"
        "    color: white;"
        "}"
        "QHeaderView::section {"
        "    background-color: #E0E0E0;"
        "    border: 1px solid gray;"
        "    padding: 8px;"
        "}"
    );
}

QString FormSelectBigVeh::FormatDateTime(quint32 timestamp)
{
    if (timestamp == 0) {
        return "无限期";
    }
    
    // 将时间戳转换为QDateTime
    QDateTime dateTime = QDateTime::fromTime_t(timestamp);
    
    // 格式化为中国时间格式
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

void FormSelectBigVeh::UpdateDetailInfo(const CBigVehInfo &info)
{
    if (!m_pLblStationInfo || !m_pLblRouteInfo || !m_pLblTimeInfo || 
        !m_pLblProvinceInfo || !m_pLblVehicleInfo || !m_pLblGoodsInfo) {
        return;
    }
    
    // 站点信息：入口站和出口站名称
    QString stationText = "站点信息：";
    if (!info.enStationIds.isEmpty()) {
        QStringList enStationNames;
        foreach (const QString &stationIdStr, info.enStationIds) {
            if (!stationIdStr.isEmpty()) {
                // 按"|"分割站点ID字符串，支持多个站点ID
                QStringList stationIds = stationIdStr.split("|", QString::SkipEmptyParts);
                foreach (const QString &stationId, stationIds) {
                    QString trimmedId = stationId.trimmed();
                    if (!trimmedId.isEmpty()) {
                        enStationNames.append(GetStationName(trimmedId));
                    }
                }
            }
        }
        stationText += QString("入口站：【%1】").arg(
                    enStationNames.isEmpty() ? "【无】" : enStationNames.join(","));
    }
    
    if (!info.exStationIds.isEmpty()) {
        QStringList exStationNames;
        foreach (const QString &stationIdStr, info.exStationIds) {
            if (!stationIdStr.isEmpty()) {
                // 按"|"分割站点ID字符串，支持多个站点ID
                QStringList stationIds = stationIdStr.split("|", QString::SkipEmptyParts);
                foreach (const QString &stationId, stationIds) {
                    QString trimmedId = stationId.trimmed();
                    if (!trimmedId.isEmpty()) {
                        exStationNames.append(GetStationName(trimmedId));
                    }
                }
            }
        }
        if (!info.enStationIds.isEmpty()) {
            stationText += "  ";
        }
        stationText += QString("出口站：【%1】").arg(exStationNames.isEmpty()?"【无】":exStationNames.join(","));
    }
    m_pLblStationInfo->setText(stationText);
    
    // 通行线路
    QString routeText = QString("通行线路：%1").arg(info.Roads.isEmpty() ? "未指定" : info.Roads);
    m_pLblRouteInfo->setText(routeText);
    
    // 通行时间范围
    QString timeText = QString("通行时间：%1 至 %2")
                      .arg(FormatDateTime(info.startPassDate))
                      .arg(FormatDateTime(info.endPassDate));
    m_pLblTimeInfo->setText(timeText);
    
    // 通行省份列表
    QString provinceText = QString("通行省份：%1").arg(FormatProvinces(info.provinces));
    m_pLblProvinceInfo->setText(provinceText);
    
    // 车辆参数
    QString vehicleText = QString("车辆参数：重量%1吨，轴数%2轴，通行次数%3次，尺寸%4×%5×%6mm")
                         .arg(info.weight / 1000.0, 0, 'f', 1)  // 转换为吨
                         .arg(info.alexs)
                         .arg(info.PassCount)
                         .arg(info.Length)
                         .arg(info.width)
                         .arg(info.Height);
    if (!info.alexsLoad.isEmpty()) {
        vehicleText += QString("，轴荷：%1").arg(info.alexsLoad);
    }
    m_pLblVehicleInfo->setText(vehicleText);
    
    // 载货信息
    QString goodsText = QString("载货信息：%1").arg(info.goodsInfo.isEmpty() ? "无" : info.goodsInfo);
    m_pLblGoodsInfo->setText(goodsText);
}

QString FormSelectBigVeh::GetStationName(const QString &stationId)
{
    if (stationId.isEmpty()) {
        return stationId;
    }
    
    // 获取COrgBasicInfoTable实例
    COrgBasicInfoTable *pOrgTable = (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
    if (!pOrgTable) {
        return stationId;
    }
    
    COrgBasicInfo orgBasicInfo;
    // 使用江西省份ID(36)查询
    if (pOrgTable->QryOrgBasicInfo(stationId, orgBasicInfo)) {
        return orgBasicInfo.sName;
    }
    
    // 如果查询失败，返回原始站点ID字符串
    return stationId;
}

QString FormSelectBigVeh::FormatProvinces(const QStringList &provinces)
{
    if (provinces.isEmpty()) {
        return "未指定";
    }
    
    QStringList provinceNames;
    
    foreach (const QString &provinceCode, provinces) {
        bool ok;
        quint16 areaCode = provinceCode.toUShort(&ok);
        
        if (ok && m_pAreaCodeTable) {
            CAreaCode areaInfo;
            if (m_pAreaCodeTable->QryAreaCode(areaCode, &areaInfo)) {
                // 查询成功，使用省份名称
                provinceNames.append(areaInfo.sAreaName);
            } else {
                // 查询失败，保留原数字代码
                provinceNames.append(provinceCode);
            }
        } else {
            // 转换失败或表不存在，保留原代码
            provinceNames.append(provinceCode);
        }
    }
    
    return provinceNames.join("、");
}

QString FormSelectBigVeh::FormatVehicleColor(int colorCode)
{
    // 根据车辆颜色代码返回颜色名称
    switch (colorCode) {
        case 0:return "蓝";
        case 1: return "黄";
        case 2: return "黑";
        case 3: return "白";
        case 4: return "渐绿";
        case 5: return "黄绿";
        case 6: return "蓝白";
        case 7: return "临";
        case 9: return "未知";
        case 11: return "绿";
        case 12: return "红";
        default: return QString::number(colorCode);
    }
}
