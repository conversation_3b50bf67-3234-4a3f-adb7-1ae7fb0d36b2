#ifndef TRANSINFO_H
#define TRANSINFO_H

#include "calcfee.h"
#include "cardfiledef.h"
#include "cardfiledef_cpc.h"
#include "cemvehlist.h"
#include "cfarecalctypes.h"
#include "cgallroadfare.h"
#include "cgreencarreservetabble.h"
#include "coldsysproc.h"
#include "gbmsgtype.h"
#include "jxlanetype.h"
#include "lanetype.h"
#include "provincemsg.h"
#include "rsudevtype.h"
#include "weighttype.h"
#include "paramfile.h"
#include "vcrdev.h"

struct CAutoRegInfo
{
    //车牌识别流水号
    int nDevIndex;
    QString id;
    CVehClass AutoVehClass;  //识别车型
    QString sAutoVehPlate;   //识别车牌
    quint8 nAutoVLPColor;    //识别车牌颜色

    QDateTime AuRegTime;        //识别时间
    QString sBigFileName;       //大图
    QString sRelativeFileName;  //
    QString ssmallFileName;     //
    QString svalueFileName;

public:
    bool IsValid(bool bCheckTime = true)
    {
        if (0 == id.length()) {
            return false;
        }
        int nMaxSeconds = 1 == nDevIndex ? 300 : 20;
        if (bCheckTime) {
            if (AuRegTime.isValid() &&
                qAbs(AuRegTime.secsTo(QDateTime::currentDateTime())) < nMaxSeconds) {
                if (VC_None != AutoVehClass || sAutoVehPlate.length() > 0) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }

        } else {
            return true;
        }
    }

    void ClearResult()
    {
        id.clear();
        nDevIndex = 0;
        AutoVehClass = VC_None;
        sAutoVehPlate.clear();
        nAutoVLPColor = 0;
        sBigFileName.clear();
        sRelativeFileName.clear();
        ssmallFileName.clear();
        svalueFileName.clear();
    }
};

enum CTransResult
{
    Tr_None,      //未交易完成
    Tr_Failed,    //交易失败
    Tr_Successed  //交易成功
                  // Tr_ZeroSuccessed   //0元交易失败，但放行
    // Tr_ReGetTacFailed   //写卡失败，但扣款成功，重新取TAC码失败
};

enum CFeeClass
{
    FeeClass_None,
    FeeClass_OBU,                // OBU累计优惠后
    FeeClass_OBUDiscountBefore,  // OBU累计优惠前
    FeeClass_Card,               // CPC卡内合计数
    FeeClass_ProvCenter,         //省中心在线计费
    FeeClass_MinistryCenter,     //部中心
    FeeClass_Min,                //最短路径
    FeeClass_Open = 11           //开放式
};

inline QString GetFeeClassName(CFeeClass feeClass)
{
    switch (feeClass) {
        case FeeClass_OBU:
        case FeeClass_OBUDiscountBefore:
            return QString("OBU计费");
            break;
        case FeeClass_Card:
            return QString("卡内计费");
            break;
        case FeeClass_ProvCenter:
        case FeeClass_MinistryCenter:
            return QString("在线计费");
            break;
        case FeeClass_Min:
            return QString("最小费额");
            break;
        default:
            break;
    }
    return QString("计费方式错误");
}

// ETC交易类型
enum CETCTransType
{
    TransType_None,
    TransType_EnNormal,        //正常发卡扣费,ef01+19
    TransType_ExNormal,        // 19+ef04
    TransType_EnOnlyAppTrans,  //仅复合消费 19
    TransType_ExOnlyAppTrans,
    TransType_abNormal,              //异常扣费（需要拦截）
    TransType_EnNoCard,              // ef04(BB+入口信息)入欧
    TransType_EnStoreNoBlance,       // ef04 (aa+入口)
    TransType_ExStoreCardNoBalance,  // 0-消费 -清空EF04
    TransType_ExBlack                //
};

class CTransInfo
{
public:
    enum CTransState
    {
        Ts_WaitTransBegin,
        Ts_IsProcessOBUBaseInfo,
        Ts_WaitOBUVehInfo,
        Ts_WaitIccInfo,
        Ts_IsReadingIcc,  //设置正在读卡状态，此时避免其他操作干预当前交易
        Ts_WaitOpResult,  //发送写卡指令，等待操作结果
        Ts_WaitMoney,     //等待收费，人工操作写卡成功，等待付费时处理。
        Ts_WaitToSave,    //交易完成，等待保存流水·
        Ts_Finished       //数据保存
    };

public:
    quint32 dwOBUID;
    COBUBaseInfo OBUBaseInfo;
    COBUVehInfo OBUVehInfo;  //
    CVehInfo VehInfo;        //车辆信息
    CMediaType mediaType;    //介质类型
    CRsuIccInfo IccInfo;     // etc卡信息
    CCPCIccInfo cpcIccInfo;  // cpc卡信息
    bool m_bBadCard;         //坏卡
    QString m_sPaperId;      //纸卡卡号
    QString m_sPaperKey;     //纸卡key

    bool m_bNoCard;  //无卡

    CVehEntryInfo vehEntryInfo;  //车辆入口信息
    CRsuOpResult RsuOpResult;
    CProCardConsumeInfo ConsumeInfo;  // ETC交易结果信息，金额、余额等

    CTransState transState;  //交易状态
    time_t transStateTime;   //状态时间，单位秒

    CTransResult transResult;  //交易结果，只有状态(transState)是Ts_Finished 才有意义
    CETCTransType etcTransType;  //交易类型-只针对ETC交易

    qint32 nErrorCode;      //交易失败结果错误码
    CVehPosInfo curVehPos;  //车辆位置帧数据
    quint8 curFrameId;      //当前帧Id
    qint64 curFrameTime;    //当前帧时间毫秒
    QString m_sError;
    bool bReGetTac;  //重新获取TAC值
    bool m_bForTac;
    time_t tmDetectTime;  //线圈检测时间
    int nDetectNum;       //检测数
    qint64 m_BeginTime;
    qint64 m_endTime;
    QDateTime TransTime;        //交易时间
    CAutoRegInfo AutoRegInfo;   // 车牌识别结果信息
    QString sCapImageFileName;  //视频抓拍图像文件名

    quint8 feeBoardPlay;       //费显模式 1-显示金额 2-警告
    CFeeClass actualFeeClass;  //计费方式
    qint32 nMinPerCent;        //最短路径金额百分比

    //门架计费信息
    TradingInfo tradInfo;
    CGantryFeeInfo gantryFeeInfo;

    CFareQryCondition m_fareQryCondition;
    CFareQryResult m_fareQryResult;

    quint8 bHolidayFree;
    bool bManNum8;             // 89座不免费，只有节假日免费时填
    bool bIsFree;              //是否是免费通行
    char GBSpEvent[GBSP_End];  //国标特情
    bool bInvoice;             //是否闯关
    quint8 OncePaySpEvent[OncePay_Sp_End];

    CAllRoadMinFeeInfo MinFeeInfo;   //最小费额通行费
    CAllRoadMinFeeInfo MinFeeInfo1;  //货1最小费率 大件车
    CVirGantryInfo m_curGantryInfo;  //当前门架信息
    bool m_bOpenGantry;              //是否经过开放式门架
    QString m_sVLPId;                //开放式车牌识别流水号
    QString m_sVehicleSignId;  //本车道生成的门架车牌识别流水id，当m_sVLPId为空时，生成

    quint64 lSpeedStartTime;
    quint64 lSpeedStopTime;

    qint32 m_nTotalFee;             //总应收金额 =交易金额+中心优惠+折扣优惠
    qint32 m_nTransFee;             //交易金额   =最终收费金额（扣款）
    qint32 m_nProvFee;              //本省计费金额
    qint32 m_nProvinceDiscountFee;  //中心优惠金额
    qint32 m_nOriginFee;            //中心优惠前金额
    qint32 m_nDiscountType;  //省中心优惠类型 1-绿通 2-联合收割 7-抢险救灾 3-j1 4-j2 5-假日（没用）
                             // 6-疫苗 7-抢险救灾
    qint32 m_nDiscountFee;    //折扣优惠金额
    qint32 m_nCardCost;       //卡成本
    quint32 m_nTotalMiles;    //计费里程，单位米
    qint32 m_nProvinceCount;  //省份总数,显示货车二维码用

    bool m_bReserved;          //是否倒车了,用于线圈自动检测到倒车，不是废票
    time_t m_InQueTime;        //车辆进队列时间
    bool m_bWhiteVeh;          //白名单车辆
    bool m_bEmVeh;             //紧急车辆
    CEmVehInfo m_emVehInfo;    //紧急车辆信息
    bool m_bGreenCheckFailed;  //绿通查验不通过

    quint32 m_nPsamSerial;  //终端交易序列号,扣费时保存
    quint8 m_TermCode[6];   //终端机编号,扣费时保存
    int m_nRsuIndex;  //天线索引号，对应设备交易Id，0-前天线 1-后天线 2-人工交易
    quint32 m_nCalcMaxFee;  //与最终收费上限比较，小于上限，按累计金额计费
    int m_nFeePercent;      //动态费率百分比，缺省为0，有值时表示启用。

    quint8 m_bVehState;  // 0-大件 1-非优惠 2-绿通 3-联合 4-集装箱 5-疫苗 6-应急
    CVehAxisInfo m_vehAxisInfo;
    quint32 m_dwToTalWeight;
    quint32 m_dwWeightLimit;
    int m_nOverRate;        //超限率 千分比
    QString m_sCertNo;      //大件运输单号
    quint64 m_llInvoiceId;  //打印票号
    int m_nInvoiceCnt;      //票据张数

    QString m_sListNo;     //发票凭证号
    QString m_splainText;  //发票凭证号明文

    QString m_sQrCode;  //发票二维码
    QString m_sOpId;    //移动支付opId

    CTransPayType m_transPayType;  //交易支付类型
    CPayType m_payType;            //支付类型
    QString m_sId;  //流水号，只有出口车道会填写。入口都是发报文时获取的
    qint8 m_bCardMgrIndex;  //当前卡片对应的卡机工位 1-上 2-下 0-非卡机卡
    CProCardBasicInfo m_PayCardInfo;

    QString m_payOrderNum;  //第三方支付信息,移动支付产生的订单号
    QString m_payCode;      //支付码信息（支付标识）
    int m_nPayChannel;      //支付渠道,旧系统为0，不处理
    int m_nPayNetType;      //支付网络

    bool m_bBlackCard;  //
    int m_nPrepayList;  // 1-未补费 2-已补费 0-非追缴名单车辆
    int m_nRepayType;   //补费类型 1-逃费 2-其他

    quint32 m_nOutTimes;  //超时时间
    bool m_bRepay;        //补费
    bool m_bUReturn;      // U型
    bool m_bReprint;      //重打

    bool m_bPassPermit;      //是否通行许可
    COperInfo m_operInfo;    //班长授权信息
    QDateTime m_AuthTime;    //授权时间
    QString m_sTmpFileName;  //是否保存临时流水

    bool m_bEnterQueue;  //已经进入队列

    VcrResult m_vcrResult;  //车型识别结果
    bool bIsJC;             //如果是手动收费则不判断最短费率（机场）

private:
    quint32 ConsumeMoney;  //扣费金额，不一定等于实收金额
    FeeInfo feeInfo;
    //出口已写卡（卡内信息已读取，并写了入口标志)
    bool m_bWriteCard_Exit;

    FeeInfoL m_feeInfoL;   //差异化计费信息
    bool m_bDifferentFee;  //差异化计费

    quint8 m_nAxisNum;

public:
    bool bSpecialVeh(QString &sVehPlate);
//    bool IsLocalETCPoliceVeh(QString &sVehPlate);
    //是否已经写卡
    bool hasWriteCard_Exit() { return m_bWriteCard_Exit; }
    //设置写卡标志
    void SetWriteCard_Exit(bool bSuccess) { m_bWriteCard_Exit = bSuccess; }

    qint64 GetVehSpeed();
    CTransInfo() { ClearTransInfo(); }
    CTransInfo(const CTransInfo &TransInfo)
    {
        ClearTransInfo();
        *this = TransInfo;
    }

    void SetTransState(CTransState tsState);

    void SetWhiteListVeh(bool bValue) { m_bWhiteVeh = bValue; }

    bool bTransOk();
    void ClearTransInfo();
    bool IsWhiteListVeh() { return m_bWhiteVeh; }

    void SetReGetTacFlag(bool bFlag, const QDateTime &NoTacTime)
    {
        bReGetTac = bFlag;
        // fortac 报文要求将入口时间改为notac的出口时间，暂不使用
        // vehEntryInfo.EnTime = NoTacTime;
    }
    void SetOBUBaseInfo(quint32 OBUID, const COBUBaseInfo *pOBUBaseInfo);

    void SetOBUVehInfo(const COBUVehInfo *pOBUVehInfo, const CVehInfo *pVehInfo);

    void SetVehInfo(const CVehInfo *pVehInfo, const CVehAxisInfo *pVehAxisInfo = NULL);

    void SetAutoVehInfo(const CVehInfo *pAutoVehInfo);

    void SetCapImageFileName(const QString &sCapImgFileName)
    {
        sCapImageFileName = sCapImgFileName;
    }

    //保存最终扣费金额
    void SetConsumeMoney(quint32 dwConsumeMoney, const QDateTime &ConsumeTime)
    {
        ConsumeMoney = dwConsumeMoney;
        TransTime = ConsumeTime;
    }

    //入口
    void SetCardTollInfo(const CCardTollInfo *pCardTollInfo, const QDateTime *pTransTime);
    //保存卡片信息
    void SetIccInfo(const CRsuIccInfo *pIccInfo);
    void SetCPCIccInfo(const CCPCIccInfo *pIccInfo);
    void SetPaperInfo(quint64 lPaperNo, const QString &sKey);

    void SetFrameId(quint8 bFrameId, quint8 bErrorCode)
    {
        curFrameId = bFrameId;
        nErrorCode = bErrorCode;
    }

    void SetVehEntryInfo(const CVehEntryInfo &vehEnInfo, const QDateTime &ExitTime);

    void ResetTransState()
    {
        transState = Ts_WaitTransBegin;
        transResult = Tr_None;
    }
    void SaveTo(CTransInfo &TransInfo);

    //有等待保存
    bool bWaitToSave() { return transState == Ts_WaitToSave; }
    //交易失败的流水
    bool bTransFailed() { return transResult == Tr_Failed; }

    bool bWaitOpResult() { return transState == Ts_WaitOpResult; }

    //判断是否重复交易

    bool CheckRepeatTransByCard(const CRsuIccInfo *pIccInfo, int mSeconds = 0);
    bool CheckHasConsumeMoney(const CRsuIccInfo *pIccInfo);
    //
    void CompleteTrans(int nDevIndex, CTransPayType transPayType, const CRsuOpResult *pRsuOpResult,
                       CTransResult bTransResult, CTransState state = CTransInfo::Ts_WaitToSave);

    void SetConsumeInfo(const CProCardConsumeInfo &CardConsumeInfo);
    void SetPayCardInfo(const CProCardBasicInfo &proCardBasicInfo,
                        CProCardConsumeInfo &cardConsumeInfo);

    void SetTerminateCode(quint8 bTerminateCode[6]);
    QString GetTransStateStr();

    qint64 GetTotalTime();

    void FillDealStatus(char DealStatus[256]);
    void operator=(const CTransInfo &TransInfo);
    void SetDetectCnt(int nDetectCnt);
    void SetSaveResult(bool bRlt);

    //返回实际扣款金额
    quint32 GetConsumeMoney();
    //取入口实收和实际扣费-门架
    quint32 GetLastMoney_Entry(qint32 &LastFee, qint32 &ConsumeFee);
    //该函数用于已经扣费后调用，不能用于扣费过程中使用
    quint32 GetLastMoney_Exit(quint32 &ConsumeFee);

    //取应收金额（计算应收金额）和计费方式 -出口

    bool CheckLastMoney_Exit(CFeeClass &feeClass, int nTransFee, qint32 &nPercent, bool isU,
                             QString &sError, bool isLocal, bool bFree);

    bool CheckMinFee(qint32 totalFee, CFeeClass &feeClass, qint32 &nPercent, bool isU, bool isLocal,
                     bool bFree, QString &sError);
    int CompareMinFee(qint32 totalFee, CFeeClass feeClass, qint32 &nPercent, bool isLocal,
                      bool bFree, QString &sError);

    int CompareMinFee_New(qint32 totalFee, CFeeClass feeClass, qint32 &nPercent, bool isLocal,
                          bool bFree, QString &sError, qint32 nLowPercent = 0, qint32 nHighPercent = 0);

    int CompareMinFeeByParam(qint32 totalFee, bool bETC, int LowPercent, int HighPercent,
                             qint32 &nPercent, bool bFree, QString &sError);

    //取应实收收通行费
    bool GetTollMoney_Exit(qint32 &totalPayFee, qint32 &totalRealFee, qint32 &nCardCost);
    //获取应付金额
    qint32 GetNeedPayMoney() const { return m_nTransFee + m_nCardCost; }

    void SetCalcMoneyToEF04_Exit(bool bFree, qint32 nDiscountType = 0);
    //汇总金额。 bETCTrans 是否etc交易 true -最小计费去95折
    void SumCalcMoney_Exit(CFeeClass nFeeClass, bool bFree, bool bETCTrans,
                           qint32 nDiscountType = 0);
    //根据最新计费信息刷新总的计费金额
    void RefreshMoney(bool bFree, qint32 nDiscountType);

    void SetLastMoney(qint32 nPayFee, qint32 nRealMoney, quint32 dwCardMoney, CFeeClass bFeeClass,
                      quint32 minPercent);
    void GetAllMoneyInfo(qint32 &dwTotalFee, qint32 &dwDisCountFee, qint32 &dwFee,
                         qint32 &dwLocalFee, qint32 &dwConsumeFee, qint32 &collectFee);

    //入口填写EF04
    void FillEF04Info_Entry(CPro0019Raw_NewGB *pRaw0019);

    void SetAllRoadMinFeeInfo(const QList<CMinFeeInfo_Province> &minFeeGroup);
    void SetAllRoadMinFeeInfo(const CAllRoadMinFeeInfo &minFeeInfo,
                              const CAllRoadMinFeeInfo *pMinFeeInfo1);

    void GetMinFeeInfo(qint32 &minFee, quint32 &Miles);

    QString GetPassId() const;
    QString GetPassId_New() const;
    quint8 GetFeeBoard(QString &sMsg);

    void SetTradeFeeInfo(const TradingInfo &tradingInfo, const FeeInfo &fee, bool isJC);

    QString GetTransTypeDesc();

    void SetGBSpEvent(GBSPEVENTID spEventId, bool bStatus = true);
    void SetOncePaySpEvent(OncePay_SpEvent spEventId, bool bStatus = true)
    {
        OncePaySpEvent[spEventId] = bStatus ? 1 : 0;
    }

    QString GetOncePaySpTypes();

    void SetEmVehInfo(bool bEmVeh, const CEmVehInfo &emVehInfo);

    bool QryFare_OnLine(CFareQryCondition &qryCondition, CFareQryResult &fareResult,
                        QString &sError, bool bDiscount);
    bool QryFare_OnLine_Union(CFareQryCondition &qryCondition, CFareQryResult &fareResult,
                              QString &sError, bool bDiscount, const QDateTime &TransTime);

    bool QryFare_OnLine_Prov(CFareQryCondition &qryCondition, CFareQryResult &fareResult,
                             QString &sError, bool bDiscount, const QDateTime &TransTime);
    void SetFareQryResult(const CFareQryCondition &qryCondition, const CFareQryResult &fareResult);
    void SetGBVehType(CUnionVehType gbVehType);

    bool VerifyAreaCode_ProCard(const CProCardBasicInfo &proCardBasicInfo, qint32 &nErrorCode,
                                QString &sError);
    bool VerifyProCardBasicInfo(const CVehInfo &VehInfo, const CProCardBasicInfo &proCardBasicInfo,
                                qint32 &nErrorCode, QString &sError);

    void GetPro0019RawForExit(CPro0019Raw_NewGB &Raw0019);

    void SetVirGantryInfo(const CVirGantryInfo &gantryInfo, bool bOpen, const QString &sVLPId);

    bool CheckOutTime(QString &sError);

    void SetVcrResult(const VcrResult *pVcrResult);

    bool SetFeeInfoL(const FeeInfoL &feeInfoL);
    void ClearFeeInfoL();

    bool ProcessDiffFeeInfo(bool bLocal);

    void GetFeeInfoL(QString &disCode, QString &feeInfo1);

    int GetVehAxisNum(bool bForEntry = false);

    bool CheckVehEntryAxisNum(QString &sError);

private:
    void FreeCalcMoney_Exit(int nDiscountType);
};

#endif  // TRANSINFO_H
